# DataFlow Pipeline Configuration - QA Generation with Parallel Processing
# This configuration defines a QA generation pipeline for SFT data creation
# with optimized parallel processing for large datasets

name: "unicom_qa_generation_pipeline"
description: "QA generation pipeline for creating SFT training data from domain documents with parallel support"

# Storage configuration
storage:
  input_path: "/home/<USER>/datasets/domain_行业数据/CSG行业数据/qa_chunked_text.json"
  output_path: "/home/<USER>/datasets/cache/sft/qa_output.jsonl"
  cache_path: "/home/<USER>/datasets/cache/sft/qa"
  file_name_prefix: "qa_generation_step"
  cache_type: "jsonl"

# LLM service configuration
llm:
  api_url: "http://134.81021.32.16322:8008/v1/chat/completions"
  model_name: "DeepSeek-V3-0324"
  max_workers: 2
  temperature: 0.7
  max_tokens: 2048
  timeout: 60

# Pipeline execution settings
batch_size: 50
parallel_workers: 4              # Number of parallel workers for processing
retry_attempts: 3
log_level: "INFO"

# Parallel processing settings
chunk_processing: true           # Enable automatic chunking for large JSONL files
max_chunk_size: 5000            # Smaller chunks for LLM processing to avoid memory issues

# Operators configuration
operators:
  - name: "sft_qa_generator"
    type: "qa_generation"
    enabled: true
    input_key: "raw_chunk"
    parameters:
      custom_prompt: |
        你是一个专业的大模型训练数据生成助手，目标是从通信行业的标准、手册、指南等技术文档中提取 ShareGPT 格式的单轮问答数据，用于构建高质量中文对话语料。

        请根据以下提供的文档片段，生成高质量的单轮对话数据。每条对话仅包含一个用户问题和一个助手回答，结构清晰、内容准确。

        生成要求：
        - 每条对话为一个 JSON 对象，字段为：
          - conversations：一个数组，包含两条消息
            - {"role": "user", "content": "..."} 表示用户提问
            - {"role": "assistant", "content": "..."} 表示助手回答
        - 语言为中文
        - 用户问题要自然真实，覆盖通信技术相关概念、配置方法、原理解释等
        - 助手回答要专业、准确、通俗易懂，不添加无依据内容
        - 输出为 5 条对话，格式为 5 个独立的 JSON 对象（非数组），每行为一条
        - 不要添加任何解释性文字、额外标点或提示

        {custom_section}

        请从文档中提炼出 5 条单轮对话，使用如下格式：

        {"conversations": [{"role": "user", "content": "5G 中的 gNB 是什么？"}, {"role": "assistant", "content": "gNB 是 5G 无线接入网的基站节点，负责与用户设备通信并连接核心网。"}]}
        {"conversations": [{"role": "user", "content": "在 NR 中，UE 是如何完成初始接入的？"}, {"role": "assistant", "content": "UE 首先接收同步信号块（SSB），完成时间和频率同步，然后通过 RACH 发起随机接入过程。"}]}
