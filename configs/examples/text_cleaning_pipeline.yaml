# DataFlow Pipeline Configuration - Text Cleaning with Parallel Processing
# This configuration defines a comprehensive text cleaning pipeline
# with optimized parallel processing for large JSONL files

name: "unicom_text_cleaning_pipeline"
description: "Comprehensive text cleaning pipeline for Unicom data processing with parallel support"

# Storage configuration
storage:
  input_path: "/home/<USER>/datasets/domain_行业数据/CSG行业数据/part_ac_560_chunked.jsonl"
  output_path: "/home/<USER>/datasets/cache/general_cleaning/cleaned_output.jsonl"
  cache_path: "/home/<USER>/datasets/cache/general_cleaning"
  file_name_prefix: "text_cleaning_step"
  cache_type: "jsonl"

# Pipeline execution settings
batch_size: 100
parallel_workers: 4              # Number of parallel workers for processing
retry_attempts: 3
log_level: "INFO"

# Parallel processing settings
chunk_processing: true           # Enable automatic chunking for large JSONL files
max_chunk_size: 10000           # Maximum lines per chunk when splitting files

# Operators configuration
operators:
  - name: "comprehensive_text_cleaning"
    type: "text_cleaning"
    enabled: true
    input_key: "raw_chunk"
    parameters:
      # Word count filtering
      min_words: 5
      max_words: 100000
      
      # Sentence count filtering
      min_sentences: 3
      max_sentences: 7500
      
      # Deduplication settings
      dedup_threshold: 0.9
      dedup_num_perm: 128
      dedup_ngram: 5
      
      # Content quality filters
      ellipsis_threshold: 0.3
      mean_word_length_min: 3
      mean_word_length_max: 10
      symbol_ratio_threshold: 0.4
      
      # ID card and sensitive information
      id_card_threshold: 3
      
      # Watermark detection
      watermarks:
        - "Copyright"
        - "Watermark"
        - "Confidential"
        - "版权所有"
        - "保留所有权利"
      
      # Special character filtering
      curly_bracket_threshold: 0.025
      unique_words_threshold: 0.1
      char_number_threshold: 100
      bulletpoint_threshold: 0.9
      javascript_threshold: 3
