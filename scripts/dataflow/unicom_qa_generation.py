from dataflow.operators.generate import (
    AutoPromptGenerator,
    QAGenerator,
    QAScorer
)

from dataflow.operators.filter import (
    ContentChooser
)

from dataflow.utils.storage import FileStorage
from dataflow.serving import APILLMServing_request
from dataflow.serving import LocalModelLLMServing_vllm
import datetime, json

class QA_APIPipeline():
    def __init__(self):

        self.storage = FileStorage(
            first_entry_file_name="/home/<USER>//datasets/domain_行业数据/CSG行业数据/qa_chunked_text.json",
            cache_path="./cache_qa",
            file_name_prefix="202508071257_dataflow_qa_cache_step",
            cache_type="jsonl",
        )

        # use API server as LLM serving
        self.llm_serving = APILLMServing_request(
                api_url="http://*************:8008/v1/chat/completions",
                model_name="DeepSeek-V3-0324",
                max_workers=10
        )

        self.prompt_generator_step1 = AutoPromptGenerator(self.llm_serving)

        self.qa_generator_step2 = QAGenerator(self.llm_serving)

        self.qa_scorer_step3 = QAScorer(self.llm_serving)
        
    def forward(self):

        self.prompt_generator_step1.run(
            storage = self.storage.step(),
            input_key = "raw_chunk"
        )

        self.qa_generator_step2.run(
            storage = self.storage.step(),
            input_key="raw_chunk",
            prompt_key="generated_prompt",
            output_quesion_key="generated_question",
            output_answer_key="generated_answer"
        )

        self.qa_scorer_step3.run(
            storage = self.storage.step(),
            input_question_key="generated_question",
            input_answer_key="generated_answer",
            output_question_quality_key="question_quality_grades",
            output_question_quality_feedback_key="question_quality_feedbacks",
            output_answer_alignment_key="answer_alignment_grades",
            output_answer_alignment_feedback_key="answer_alignment_feedbacks",
            output_answer_verifiability_key="answer_verifiability_grades",
        )
        
if __name__ == "__main__":
    model = QA_APIPipeline()
    entry_file_name = "/home/<USER>/datasets/domain_行业数据/CSG行业数据/domain_qa_aa.jsonl"

    time_now = datetime.datetime.now().strftime("%Y%m%d%H%M%S")

    # 读取文件，拿到 raw_content的值，也就是要处理的文件
    with open(entry_file_name, 'r') as f:
        lines = f.readlines()
        for line in lines:
            data = json.loads(line)
            raw_content = data['chunk_path']
            if raw_content:
              print(f"处理： {raw_content}")

              file_name = raw_content.split('/')[-1].split('.')[0]

              model.storage = FileStorage(
                  first_entry_file_name=raw_content,
                  cache_path="./cache_qa",
                  file_name_prefix=f"{time_now}_{file_name}_qa_aa_step",
                  cache_type="jsonl",
              )

              model.forward()
            else:
              print(f"跳过： {data}")
