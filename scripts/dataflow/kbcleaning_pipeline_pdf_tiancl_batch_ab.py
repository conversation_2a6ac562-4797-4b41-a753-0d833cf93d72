from dataflow.operators.generate import (
    CorpusTextSplitterBatch,
    FileOrURLToMarkdownConverterBatch,
    KnowledgeCleanerBatch,
    MultiHopQAGeneratorBatch,
)
from dataflow.utils.storage import FileStorage
from dataflow.serving import APILLMServing_request

class KBCleaningPDF_APIPipeline():
    def __init__(self):

        self.storage = FileStorage(
            first_entry_file_name="/home/<USER>/datasets/domain_行业数据/CSG行业数据/part_ab.jsonl",
            cache_path="./.cache/api",
            file_name_prefix="202508041618_pdf_cleaning_step",
            cache_type="jsonl",
        )

        self.llm_serving = APILLMServing_request(
                api_url="http://*************:8008/v1/chat/completions",
                model_name="DeepSeek-V3-0324",
                max_workers=100
        )

        #self.knowledge_cleaning_step1 = FileOrURLToMarkdownConverter(
        #    intermediate_dir="../example_data/KBCleaningPipeline/raw/",
        #    lang="zh",
        #    #mineru_backend="vlm-sglang-engine",
        #    mineru_backend="pipeline",
        #)
        self.knowledge_cleaning_step1 = FileOrURLToMarkdownConverterBatch(
            intermediate_dir="../example_data/KBCleaningPipeline/part_ab/",
            lang="zh",
            #mineru_backend="vlm-sglang-engine",
            mineru_backend="pipeline",
        )

        self.knowledge_cleaning_step2 = CorpusTextSplitterBatch(
            split_method="sentence",
            chunk_size=2048,
            #tokenizer_name="Qwen/Qwen2.5-7B-Instruct",
            #tokenizer_name="/home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen2.5-7B-Instruct",
            tokenizer_name="/home/<USER>/yulan/YuLan-Mini",
        )

        self.knowledge_cleaning_step3 = KnowledgeCleanerBatch(
            llm_serving=self.llm_serving,
            lang="zh"
        )

        #self.knowledge_cleaning_step4 = MultiHopQAGeneratorBatch(
        #    llm_serving=self.llm_serving,
        #    lang="zh"
        #)

    def forward(self, url:str=None, raw_file:str=None):
        extracted=self.knowledge_cleaning_step1.run(
            storage=self.storage.step(),
        )
        
        self.knowledge_cleaning_step2.run(
            storage=self.storage.step(),
        )

        self.knowledge_cleaning_step3.run(
            storage=self.storage.step(),
        )
        #self.knowledge_cleaning_step4.run(
        #    storage=self.storage.step(),
        #)
        
if __name__ == "__main__":
    model = KBCleaningPDF_APIPipeline()
    model.forward()
