from dataflow.operators.generate import (
    CorpusTextSplitterBatch,
    FileOrURLToMarkdownConverterBatch,
    KnowledgeCleanerBatch,
    MultiHopQAGeneratorBatch,
)
from dataflow.utils.storage import FileStorage
from dataflow.serving import APILLMServing_request

class Chunked2LLMClearning_Pipeline():
    def __init__(self):

        self.storage = FileStorage(
            first_entry_file_name="/home/<USER>/datasets/domain_行业数据/CSG行业数据/part_aa_329_md_chunked.jsonl",
            cache_path="./.cache/api",
            file_name_prefix="202508071120_llmcleaning_part_aa_329_step",
            cache_type="jsonl",
        )

        self.llm_serving = APILLMServing_request(
                api_url="http://134.80.321.1111:8008/v1/chat/completions",
                model_name="DeepSeek-V3-0324",
                max_workers=10
        )

        self.knowledge_cleaning_step1 = KnowledgeCleanerBatch(
            llm_serving=self.llm_serving,
            lang="zh"
        )

    def forward(self, url:str=None, raw_file:str=None):
        
        self.knowledge_cleaning_step1.run(
            storage=self.storage.step(),
        )

if __name__ == "__main__":
    model = Chunked2LLMClearning_Pipeline()
    model.forward()
