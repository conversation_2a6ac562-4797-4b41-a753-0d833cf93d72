from dataflow.operators.generate import (
    CorpusTextSplitterBatch,
)
from dataflow.utils.storage import FileStorage

class MD2ChunkPipeline():
    def __init__(self):

        self.storage = FileStorage(
            #first_entry_file_name="/home/<USER>/datasets/domain_行业数据/CSG行业数据/part_aa_329_md.jsonl",
            #first_entry_file_name="/home/<USER>/datasets/domain_行业数据/CSG行业数据/part_ad_431_md_chunked.jsonl",
            first_entry_file_name="/home/<USER>/datasets/domain_行业数据/CSG行业数据/part_ac_560.jsonl",
            cache_path="./.cache/api",
            file_name_prefix="202508071034_ac_md_chunk_step",
            cache_type="jsonl",
        )

        self.knowledge_cleaning_step1 = CorpusTextSplitterBatch(
            split_method="sentence",
            chunk_size=2048,
            tokenizer_name="/home/<USER>/yulan/<PERSON><PERSON><PERSON>-<PERSON>",
        )

    def forward(self, url:str=None, raw_file:str=None):
        
        self.knowledge_cleaning_step1.run(
            storage=self.storage.step(),
        )

if __name__ == "__main__":
    model = MD2ChunkPipeline()
    model.forward()
