

import json
import random
import argparse
import os


def shuffle_jsonl(input_file, output_file, seed=None):
    """
    打乱JSONL文件的行顺序

    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径
        seed (int, optional): 随机种子，用于确保结果可重复
    """
    print(f"正在读取文件: {input_file}")

    # 读取所有行
    lines = []
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if line:  # 跳过空行
                try:
                    # 验证JSON格式
                    json.loads(line)
                    lines.append(line)
                except json.JSONDecodeError as e:
                    print(f"警告: 第 {line_num} 行JSON格式错误，已跳过: {e}")

            # 显示进度
            if line_num % 10000 == 0:
                print(f"已读取 {line_num} 行...")

    print(f"读取完成，共 {len(lines)} 行有效数据")

    # 设置随机种子
    if seed is not None:
        random.seed(seed)
        print(f"使用随机种子: {seed}")

    # 打乱顺序
    print("正在打乱数据...")
    random.shuffle(lines)

    # 写入打乱后的数据
    print(f"正在写入文件: {output_file}")
    with open(output_file, 'w', encoding='utf-8') as f:
        for line in lines:
            f.write(line + '\n')

    print(f"完成！已将 {len(lines)} 行数据打乱并保存到 {output_file}")


def main():
    parser = argparse.ArgumentParser(description='JSONL文件打乱工具')
    parser.add_argument('input', help='输入JSONL文件路径')
    parser.add_argument('-o', '--output', help='输出JSONL文件路径 (默认: 输入文件名_shuffled.jsonl)')
    parser.add_argument('-s', '--seed', type=int, help='随机种子 (可选，用于确保结果可重复)')

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"错误: 输入文件 '{args.input}' 不存在")
        return

    # 设置输出文件名
    if args.output:
        output_file = args.output
    else:
        base_name = os.path.splitext(args.input)[0]
        output_file = f"{base_name}_shuffled.jsonl"

    # 检查输出文件是否存在
    if os.path.exists(output_file):
        response = input(f"输出文件 '{output_file}' 已存在，是否覆盖？ (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("操作已取消")
            return

    try:
        shuffle_jsonl(args.input, output_file, args.seed)
    except Exception as e:
        print(f"处理失败: {e}")


if __name__ == "__main__":
    main()

# 使用示例:
# 1. 基本用法
#    python shuffle.py data.jsonl
#    输出: data_shuffled.jsonl
#
# 2. 指定输出文件
#    python shuffle.py data.jsonl -o shuffled_data.jsonl
#
# 3. 使用随机种子确保可重复性
#    python shuffle.py data.jsonl -s 42
#
# 4. 完整示例
#    python shuffle.py input.jsonl -o output.jsonl -s 123
