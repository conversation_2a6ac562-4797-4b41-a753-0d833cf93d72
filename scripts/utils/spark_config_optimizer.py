#!/usr/bin/env python3
"""
🔧 Spark配置优化器
根据系统资源和数据规模自动优化Spark配置参数

功能：
1. 系统资源检测
2. 智能配置推荐
3. 性能调优建议
4. 配置文件生成
"""

import os
import sys
import json
import argparse
from typing import Dict, Tuple, Optional
from pathlib import Path

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("⚠️ psutil未安装，部分功能受限")


class SystemResourceAnalyzer:
    """系统资源分析器"""
    
    def __init__(self):
        self.cpu_count = os.cpu_count() or 4
        self.memory_gb = self._get_memory_gb()
        self.disk_info = self._get_disk_info()
        
    def _get_memory_gb(self) -> float:
        """获取系统内存(GB)"""
        if PSUTIL_AVAILABLE:
            return psutil.virtual_memory().total / (1024**3)
        else:
            # 估算值
            return 8.0
            
    def _get_disk_info(self) -> Dict:
        """获取磁盘信息"""
        if PSUTIL_AVAILABLE:
            disk = psutil.disk_usage('.')
            return {
                'total_gb': disk.total / (1024**3),
                'free_gb': disk.free / (1024**3),
                'used_percent': (disk.used / disk.total) * 100
            }
        else:
            return {'total_gb': 100, 'free_gb': 50, 'used_percent': 50}
            
    def get_resource_summary(self) -> Dict:
        """获取资源摘要"""
        return {
            'cpu_cores': self.cpu_count,
            'memory_gb': self.memory_gb,
            'disk_info': self.disk_info
        }
        
    def print_resource_info(self):
        """打印资源信息"""
        print("🖥️ 系统资源分析:")
        print(f"   CPU核心数: {self.cpu_count}")
        print(f"   总内存: {self.memory_gb:.1f}GB")
        print(f"   可用磁盘: {self.disk_info['free_gb']:.1f}GB")


class SparkConfigOptimizer:
    """Spark配置优化器"""
    
    def __init__(self, resource_analyzer: SystemResourceAnalyzer):
        self.resources = resource_analyzer
        
    def calculate_memory_allocation(self, data_size_gb: Optional[float] = None) -> Dict[str, str]:
        """计算内存分配"""
        total_memory = self.resources.memory_gb
        
        # 为系统保留内存
        system_reserved = max(1, total_memory * 0.1)  # 至少1GB，最多10%
        available_memory = total_memory - system_reserved
        
        if data_size_gb:
            # 根据数据大小调整
            if data_size_gb > available_memory * 0.5:
                # 大数据集，更多内存给executor
                driver_memory = min(4, available_memory * 0.2)
                executor_memory = available_memory * 0.7
            else:
                # 小数据集，平衡分配
                driver_memory = min(2, available_memory * 0.3)
                executor_memory = available_memory * 0.6
        else:
            # 默认分配
            driver_memory = min(2, available_memory * 0.25)
            executor_memory = available_memory * 0.6
            
        return {
            'driver_memory': f"{int(driver_memory)}g",
            'executor_memory': f"{int(executor_memory)}g",
            'driver_max_result_size': f"{int(driver_memory * 0.8)}g"
        }
        
    def calculate_parallelism(self, data_size_gb: Optional[float] = None) -> Dict[str, int]:
        """计算并行度配置"""
        cpu_cores = self.resources.cpu_count
        
        # 执行器配置
        if cpu_cores <= 4:
            executor_cores = 2
            executor_instances = max(1, cpu_cores // 2)
        elif cpu_cores <= 8:
            executor_cores = 2
            executor_instances = cpu_cores // 2
        else:
            executor_cores = 4
            executor_instances = cpu_cores // 4
            
        # 默认并行度
        default_parallelism = cpu_cores * 2
        
        # 根据数据大小调整
        if data_size_gb:
            if data_size_gb > 10:  # 大数据集
                default_parallelism = cpu_cores * 4
            elif data_size_gb < 1:  # 小数据集
                default_parallelism = cpu_cores
                
        return {
            'executor_cores': executor_cores,
            'executor_instances': executor_instances,
            'default_parallelism': default_parallelism,
            'sql_shuffle_partitions': default_parallelism * 2
        }
        
    def get_performance_configs(self) -> Dict[str, str]:
        """获取性能优化配置"""
        return {
            # 序列化优化
            'serializer': 'org.apache.spark.serializer.KryoSerializer',
            'kryo_unsafe': 'true',
            'kryo_reference_tracking': 'false',
            
            # SQL优化
            'sql_adaptive_enabled': 'true',
            'sql_adaptive_coalesce_partitions_enabled': 'true',
            'sql_adaptive_skew_join_enabled': 'true',
            'sql_cbo_enabled': 'true',
            
            # 网络优化
            'network_timeout': '300s',
            'rpc_askTimeout': '300s',
            'rpc_lookupTimeout': '300s',
            
            # 存储优化
            'storage_level': 'MEMORY_AND_DISK_SER',
            'rdd_compress': 'true',
            'broadcast_compress': 'true',
            
            # Arrow优化
            'sql_execution_arrow_pyspark_enabled': 'true',
            'sql_execution_arrow_maxRecordsPerBatch': '10000'
        }
        
    def get_dynamic_allocation_configs(self) -> Dict[str, str]:
        """获取动态分配配置"""
        max_executors = min(20, self.resources.cpu_count * 2)
        
        return {
            'dynamicAllocation_enabled': 'true',
            'dynamicAllocation_minExecutors': '1',
            'dynamicAllocation_maxExecutors': str(max_executors),
            'dynamicAllocation_initialExecutors': '2',
            'dynamicAllocation_executorIdleTimeout': '60s',
            'dynamicAllocation_cachedExecutorIdleTimeout': '300s'
        }
        
    def generate_optimized_config(self, 
                                 deployment_mode: str = 'local',
                                 data_size_gb: Optional[float] = None,
                                 cluster_nodes: int = 1) -> Dict:
        """生成优化配置"""
        
        # 基础配置
        config = {
            'app_name': 'OptimizedJSONLSampler',
            'master': self._get_master_url(deployment_mode, cluster_nodes)
        }
        
        # 内存配置
        memory_config = self.calculate_memory_allocation(data_size_gb)
        config.update(memory_config)
        
        # 并行度配置
        parallelism_config = self.calculate_parallelism(data_size_gb)
        config.update({f"executor_{k}" if k.startswith('executor') else k: str(v) 
                      for k, v in parallelism_config.items()})
        
        # 性能优化配置
        performance_config = self.get_performance_configs()
        config.update(performance_config)
        
        # 动态分配配置
        if deployment_mode != 'local':
            dynamic_config = self.get_dynamic_allocation_configs()
            config.update(dynamic_config)
            
        return config
        
    def _get_master_url(self, deployment_mode: str, cluster_nodes: int) -> str:
        """获取master URL"""
        if deployment_mode == 'local':
            return f"local[{self.resources.cpu_count}]"
        elif deployment_mode == 'standalone':
            return "spark://master:7077"
        elif deployment_mode == 'yarn':
            return "yarn"
        elif deployment_mode == 'k8s':
            return "k8s://https://kubernetes.default.svc:443"
        else:
            return "local[*]"
            
    def print_recommendations(self, config: Dict):
        """打印配置建议"""
        print("\n🎯 Spark配置建议:")
        print(f"   主节点: {config['master']}")
        print(f"   驱动器内存: {config['driver_memory']}")
        print(f"   执行器内存: {config['executor_memory']}")
        print(f"   执行器核心: {config.get('executor_cores', 'auto')}")
        print(f"   默认并行度: {config.get('default_parallelism', 'auto')}")
        
        # 性能建议
        print("\n💡 性能优化建议:")
        if self.resources.memory_gb < 8:
            print("   ⚠️ 内存较少，建议增加内存或减少并行度")
        if self.resources.cpu_count < 4:
            print("   ⚠️ CPU核心较少，建议使用更多核心或减少executor数量")
        if self.resources.disk_info['free_gb'] < 10:
            print("   ⚠️ 磁盘空间不足，建议清理空间")
            
        print("   ✅ 已启用Kryo序列化优化")
        print("   ✅ 已启用SQL自适应查询优化")
        print("   ✅ 已启用Arrow优化")


def estimate_data_size(file_pattern: str) -> float:
    """估算数据大小(GB)"""
    import glob
    
    total_size = 0
    files = glob.glob(file_pattern)
    
    for file_path in files:
        if os.path.exists(file_path):
            total_size += os.path.getsize(file_path)
            
    return total_size / (1024**3)


def save_config_file(config: Dict, output_path: str, format_type: str = 'json'):
    """保存配置文件"""
    if format_type == 'json':
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    elif format_type == 'properties':
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# Spark优化配置\n")
            f.write(f"# 生成时间: {datetime.now().isoformat()}\n\n")
            for key, value in config.items():
                spark_key = f"spark.{key.replace('_', '.')}"
                f.write(f"{spark_key}={value}\n")
    elif format_type == 'yaml':
        try:
            import yaml
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        except ImportError:
            print("⚠️ PyYAML未安装，使用JSON格式")
            save_config_file(config, output_path.replace('.yaml', '.json'), 'json')


def main():
    parser = argparse.ArgumentParser(description='🔧 Spark配置优化器')
    parser.add_argument('--deployment', choices=['local', 'standalone', 'yarn', 'k8s'],
                        default='local', help='部署模式 (默认: local)')
    parser.add_argument('--data-pattern', default='*.jsonl',
                        help='数据文件模式，用于估算大小')
    parser.add_argument('--data-size-gb', type=float,
                        help='手动指定数据大小(GB)')
    parser.add_argument('--cluster-nodes', type=int, default=1,
                        help='集群节点数 (默认: 1)')
    parser.add_argument('--output', '-o', default='spark_config_optimized.json',
                        help='输出配置文件路径')
    parser.add_argument('--format', choices=['json', 'properties', 'yaml'],
                        default='json', help='配置文件格式')
    parser.add_argument('--analyze-only', action='store_true',
                        help='仅分析系统资源，不生成配置')
    
    args = parser.parse_args()
    
    print("🔧 Spark配置优化器")
    print("=" * 50)
    
    # 分析系统资源
    resource_analyzer = SystemResourceAnalyzer()
    resource_analyzer.print_resource_info()
    
    if args.analyze_only:
        return
        
    # 估算数据大小
    if args.data_size_gb:
        data_size_gb = args.data_size_gb
        print(f"\n📊 指定数据大小: {data_size_gb:.2f}GB")
    else:
        data_size_gb = estimate_data_size(args.data_pattern)
        print(f"\n📊 估算数据大小: {data_size_gb:.2f}GB")
        
    # 生成优化配置
    optimizer = SparkConfigOptimizer(resource_analyzer)
    config = optimizer.generate_optimized_config(
        deployment_mode=args.deployment,
        data_size_gb=data_size_gb,
        cluster_nodes=args.cluster_nodes
    )
    
    # 打印建议
    optimizer.print_recommendations(config)
    
    # 保存配置文件
    save_config_file(config, args.output, args.format)
    print(f"\n✅ 优化配置已保存到: {args.output}")
    
    # 使用示例
    print(f"\n🚀 使用示例:")
    print(f"python sampler_spark.py \\")
    print(f"    --master {config['master']} \\")
    print(f"    --executor-memory {config['executor_memory']} \\")
    print(f"    --driver-memory {config['driver_memory']} \\")
    print(f"    --executor-cores {config.get('executor_cores', 2)}")


if __name__ == "__main__":
    from datetime import datetime
    main()
