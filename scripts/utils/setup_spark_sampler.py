#!/usr/bin/env python3
"""
🚀 Spark抽样工具完整安装脚本
自动安装Java、Spark和相关依赖

功能：
1. 检查系统环境
2. 安装Java JDK
3. 安装PySpark
4. 配置环境变量
5. 验证安装
"""

import os
import sys
import subprocess
import platform
import shutil
import urllib.request
from pathlib import Path


class SparkEnvironmentSetup:
    """Spark环境安装器"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.arch = platform.machine().lower()
        self.java_home = None
        self.spark_home = None
        
    def check_java_installation(self) -> bool:
        """检查Java安装"""
        try:
            result = subprocess.run(['java', '-version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                version_line = result.stderr.split('\n')[0]
                print(f"✅ Java已安装: {version_line}")
                
                # 尝试获取JAVA_HOME
                self.java_home = os.environ.get('JAVA_HOME')
                if not self.java_home:
                    self.java_home = self._find_java_home()
                    
                return True
            else:
                print("❌ Java未安装")
                return False
        except FileNotFoundError:
            print("❌ Java未安装")
            return False
            
    def _find_java_home(self) -> str:
        """查找JAVA_HOME"""
        possible_paths = []
        
        if self.system == 'darwin':  # macOS
            possible_paths = [
                '/Library/Java/JavaVirtualMachines/*/Contents/Home',
                '/System/Library/Frameworks/JavaVM.framework/Versions/Current/Commands/java',
                '/usr/libexec/java_home'
            ]
        elif self.system == 'linux':
            possible_paths = [
                '/usr/lib/jvm/default-java',
                '/usr/lib/jvm/java-11-openjdk*',
                '/usr/lib/jvm/java-8-openjdk*',
                '/opt/java/openjdk'
            ]
        elif self.system == 'windows':
            possible_paths = [
                'C:\\Program Files\\Java\\jdk*',
                'C:\\Program Files (x86)\\Java\\jdk*'
            ]
            
        # 尝试使用java_home命令 (macOS)
        if self.system == 'darwin':
            try:
                result = subprocess.run(['/usr/libexec/java_home'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    return result.stdout.strip()
            except:
                pass
                
        # 查找可能的路径
        import glob
        for pattern in possible_paths:
            matches = glob.glob(pattern)
            if matches:
                return matches[0]
                
        return None
        
    def install_java(self) -> bool:
        """安装Java"""
        print("📦 开始安装Java...")
        
        if self.system == 'darwin':
            return self._install_java_macos()
        elif self.system == 'linux':
            return self._install_java_linux()
        elif self.system == 'windows':
            return self._install_java_windows()
        else:
            print(f"❌ 不支持的操作系统: {self.system}")
            return False
            
    def _install_java_macos(self) -> bool:
        """在macOS上安装Java"""
        try:
            # 检查是否有Homebrew
            if shutil.which('brew'):
                print("使用Homebrew安装Java...")
                subprocess.run(['brew', 'install', 'openjdk@11'], check=True)
                
                # 设置符号链接
                subprocess.run([
                    'sudo', 'ln', '-sfn', 
                    '/opt/homebrew/opt/openjdk@11/libexec/openjdk.jdk',
                    '/Library/Java/JavaVirtualMachines/openjdk-11.jdk'
                ], check=True)
                
                self.java_home = '/opt/homebrew/opt/openjdk@11'
                return True
            else:
                print("❌ 请先安装Homebrew或手动安装Java")
                print("Homebrew安装: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Java安装失败: {e}")
            return False
            
    def _install_java_linux(self) -> bool:
        """在Linux上安装Java"""
        try:
            # 尝试使用apt (Ubuntu/Debian)
            if shutil.which('apt'):
                print("使用apt安装Java...")
                subprocess.run(['sudo', 'apt', 'update'], check=True)
                subprocess.run(['sudo', 'apt', 'install', '-y', 'openjdk-11-jdk'], check=True)
                self.java_home = '/usr/lib/jvm/java-11-openjdk-amd64'
                return True
                
            # 尝试使用yum (CentOS/RHEL)
            elif shutil.which('yum'):
                print("使用yum安装Java...")
                subprocess.run(['sudo', 'yum', 'install', '-y', 'java-11-openjdk-devel'], check=True)
                self.java_home = '/usr/lib/jvm/java-11-openjdk'
                return True
                
            else:
                print("❌ 不支持的Linux发行版，请手动安装Java")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Java安装失败: {e}")
            return False
            
    def _install_java_windows(self) -> bool:
        """在Windows上安装Java"""
        print("❌ Windows系统请手动安装Java:")
        print("1. 访问 https://adoptium.net/")
        print("2. 下载并安装OpenJDK 11")
        print("3. 设置JAVA_HOME环境变量")
        return False
        
    def install_pyspark(self) -> bool:
        """安装PySpark"""
        try:
            print("📦 安装PySpark...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyspark'], check=True)
            print("✅ PySpark安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ PySpark安装失败: {e}")
            return False
            
    def setup_environment_variables(self):
        """设置环境变量"""
        env_vars = {}
        
        if self.java_home:
            env_vars['JAVA_HOME'] = self.java_home
            
        env_vars['PYSPARK_PYTHON'] = sys.executable
        env_vars['PYSPARK_DRIVER_PYTHON'] = sys.executable
        
        # 更新当前环境
        for key, value in env_vars.items():
            os.environ[key] = value
            
        # 生成环境配置脚本
        env_script = Path("spark_env.sh")
        with open(env_script, 'w') as f:
            f.write("#!/bin/bash\n")
            f.write("# Spark环境配置\n\n")
            for key, value in env_vars.items():
                f.write(f"export {key}='{value}'\n")
                
        env_script.chmod(0o755)
        
        print(f"✅ 环境配置已保存到: {env_script}")
        print("请运行: source spark_env.sh")
        
        return env_vars
        
    def verify_installation(self) -> bool:
        """验证安装"""
        print("🔍 验证Spark安装...")
        
        try:
            # 测试PySpark导入
            import pyspark
            print(f"✅ PySpark导入成功: {pyspark.__version__}")
            
            # 测试Spark会话
            from pyspark.sql import SparkSession
            
            spark = SparkSession.builder \
                .appName("InstallationTest") \
                .master("local[2]") \
                .config("spark.driver.host", "localhost") \
                .getOrCreate()
                
            print("✅ Spark会话创建成功")
            
            # 简单测试
            data = [("Alice", 25), ("Bob", 30), ("Charlie", 35)]
            df = spark.createDataFrame(data, ["name", "age"])
            count = df.count()
            avg_age = df.agg({"age": "avg"}).collect()[0][0]
            
            spark.stop()
            
            if count == 3 and 25 <= avg_age <= 35:
                print("✅ Spark功能测试通过")
                return True
            else:
                print("❌ Spark功能测试失败")
                return False
                
        except Exception as e:
            print(f"❌ Spark验证失败: {e}")
            return False
            
    def print_usage_guide(self):
        """打印使用指南"""
        print("\n" + "=" * 60)
        print("🚀 Spark抽样工具使用指南")
        print("=" * 60)
        
        print("\n📋 基本使用:")
        print("# 本地模式")
        print("python sampler_spark.py --input '*.jsonl' --output sampled_data --ratio 0.1")
        
        print("\n# 指定Spark配置")
        print("python sampler_spark.py \\")
        print("    --input 'data/*.jsonl' \\")
        print("    --output sampled_data \\")
        print("    --ratio 0.1 \\")
        print("    --master 'local[4]' \\")
        print("    --executor-memory 4g \\")
        print("    --driver-memory 2g")
        
        print("\n🔧 配置优化:")
        print("# 生成优化配置")
        print("python spark_config_optimizer.py --data-pattern '*.jsonl'")
        
        print("\n📊 性能对比:")
        print("# 对比三个版本性能")
        print("python benchmark_all_samplers.py")
        
        print("\n🌐 Web UI监控:")
        print("# Spark应用运行时访问")
        print("http://localhost:4040")
        
        print("\n💡 性能建议:")
        print("- 小数据集(<1GB): 使用 local[2] 或 local[4]")
        print("- 中等数据集(1-10GB): 使用 local[*]")
        print("- 大数据集(>10GB): 考虑集群模式")
        print("- 内存充足时增加 executor-memory")
        print("- CPU核心多时增加并行度")


def main():
    print("🚀 Spark抽样工具完整安装")
    print("=" * 50)
    
    setup = SparkEnvironmentSetup()
    
    # 检查Java
    if not setup.check_java_installation():
        print("\n📦 Java未安装，开始安装...")
        if not setup.install_java():
            print("❌ Java安装失败，请手动安装Java 8或11")
            print("下载地址: https://adoptium.net/")
            return
            
        # 重新检查Java
        if not setup.check_java_installation():
            print("❌ Java安装后仍无法检测到，请检查安装")
            return
            
    # 安装PySpark
    if not setup.install_pyspark():
        return
        
    # 设置环境变量
    env_vars = setup.setup_environment_variables()
    
    # 验证安装
    if setup.verify_installation():
        print("\n🎉 Spark环境安装成功!")
        setup.print_usage_guide()
    else:
        print("\n❌ Spark环境验证失败")
        print("请检查Java和PySpark安装")
        
        # 提供故障排除建议
        print("\n🔧 故障排除:")
        print("1. 确保Java 8或11已正确安装")
        print("2. 设置JAVA_HOME环境变量")
        print("3. 运行: source spark_env.sh")
        print("4. 重新运行验证: python -c 'import pyspark; print(pyspark.__version__)'")


if __name__ == "__main__":
    main()
