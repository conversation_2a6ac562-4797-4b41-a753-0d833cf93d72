#!/usr/bin/env python3
"""
🏁 全面抽样工具性能对比测试
对比原版、优化版和Spark版的性能差异

测试维度：
1. 执行时间
2. 内存使用
3. CPU利用率
4. 吞吐量
5. 扩展性
"""

import os
import sys
import time
import subprocess
import json
import tempfile
import shutil
import argparse
import threading
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import psutil


class ComprehensiveBenchmark:
    """全面性能基准测试"""
    
    def __init__(self, test_data_dir: str):
        self.test_data_dir = Path(test_data_dir)
        self.results = {}
        self.script_dir = Path(__file__).parent
        
    def create_test_datasets(self, sizes: List[Tuple[str, int, int]]) -> Dict[str, List[str]]:
        """创建不同规模的测试数据集"""
        datasets = {}
        
        print("📊 创建测试数据集...")
        
        for size_name, num_files, lines_per_file in sizes:
            print(f"   创建 {size_name} 数据集: {num_files} 文件 x {lines_per_file} 行")
            
            dataset_dir = self.test_data_dir / size_name
            dataset_dir.mkdir(parents=True, exist_ok=True)
            
            files = []
            sources = ['source_A', 'source_B', 'source_C', 'source_D']
            
            for file_idx in range(num_files):
                file_path = dataset_dir / f"data_{file_idx + 1}.jsonl"
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    for line_idx in range(lines_per_file):
                        data = {
                            'id': f"{size_name}_file{file_idx}_line{line_idx}",
                            'source': sources[line_idx % len(sources)],
                            'content': f"测试内容 {file_idx}-{line_idx} " * 20,  # 增加数据大小
                            'metadata': {
                                'file_index': file_idx,
                                'line_index': line_idx,
                                'timestamp': time.time(),
                                'size_category': size_name
                            }
                        }
                        f.write(json.dumps(data, ensure_ascii=False) + '\n')
                        
                files.append(str(file_path))
                
            datasets[size_name] = files
            total_size = sum(os.path.getsize(f) for f in files) / (1024**2)
            print(f"   ✅ {size_name}: {len(files)} 文件, {total_size:.1f}MB")
            
        return datasets
        
    def run_sampler_test(self, script_name: str, input_pattern: str, 
                        output_prefix: str, extra_args: List[str] = None) -> Dict:
        """运行采样器测试"""
        
        script_path = self.script_dir / script_name
        if not script_path.exists():
            return {'success': False, 'error': f'Script not found: {script_name}'}
            
        cmd = [
            sys.executable, str(script_path),
            '--input', input_pattern,
            '--output', output_prefix,
            '--ratio', '0.1',
            '--seed', '42'
        ]
        
        if extra_args:
            cmd.extend(extra_args)
            
        print(f"🔄 运行 {script_name}: {' '.join(cmd[-6:])}")
        
        # 性能监控
        monitor = PerformanceMonitor()
        monitor.start_monitoring()
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10分钟超时
            )
            
            end_time = time.time()
            stats = monitor.stop_monitoring()
            
            # 解析输出获取性能指标
            performance_metrics = self._parse_output_metrics(result.stdout)
            
            return {
                'success': result.returncode == 0,
                'total_time': end_time - start_time,
                'peak_memory_mb': stats['peak_memory_mb'],
                'avg_cpu_percent': stats['avg_cpu_percent'],
                'stdout': result.stdout,
                'stderr': result.stderr,
                'performance_metrics': performance_metrics,
                'memory_timeline': stats['memory_samples'][:100],  # 限制数据量
                'cpu_timeline': stats['cpu_samples'][:100]
            }
            
        except subprocess.TimeoutExpired:
            monitor.stop_monitoring()
            return {
                'success': False,
                'error': 'Timeout',
                'total_time': 600,
                'peak_memory_mb': 0,
                'avg_cpu_percent': 0
            }
        except Exception as e:
            monitor.stop_monitoring()
            return {
                'success': False,
                'error': str(e),
                'total_time': 0,
                'peak_memory_mb': 0,
                'avg_cpu_percent': 0
            }
            
    def _parse_output_metrics(self, stdout: str) -> Dict:
        """解析输出中的性能指标"""
        metrics = {
            'processing_speed': 0,
            'total_data': 0,
            'sampled_data': 0
        }
        
        for line in stdout.split('\n'):
            if '处理速度:' in line:
                try:
                    speed_str = line.split('处理速度:')[1].split('条/秒')[0].strip()
                    metrics['processing_speed'] = float(speed_str.replace(',', ''))
                except:
                    pass
            elif '原始数据:' in line:
                try:
                    data_str = line.split('原始数据:')[1].split('条')[0].strip()
                    metrics['total_data'] = int(data_str.replace(',', ''))
                except:
                    pass
            elif '本次采样:' in line or '采样完成，共' in line:
                try:
                    if '本次采样:' in line:
                        sample_str = line.split('本次采样:')[1].split('条')[0].strip()
                    else:
                        sample_str = line.split('采样完成，共')[1].split('条')[0].strip()
                    metrics['sampled_data'] = int(sample_str.replace(',', ''))
                except:
                    pass
                    
        return metrics
        
    def run_comprehensive_benchmark(self, datasets: Dict[str, List[str]]) -> Dict:
        """运行全面基准测试"""
        
        samplers = [
            ('sampler.py', '原版'),
            ('sampler_optimized.py', '优化版'),
            ('sampler_spark.py', 'Spark版')
        ]
        
        results = {}
        
        for size_name, files in datasets.items():
            print(f"\n{'='*60}")
            print(f"🧪 测试数据集: {size_name}")
            print(f"{'='*60}")
            
            results[size_name] = {}
            input_pattern = str(self.test_data_dir / size_name / "*.jsonl")
            
            for script_name, display_name in samplers:
                print(f"\n🔄 测试 {display_name} ({script_name})")
                
                output_prefix = str(self.test_data_dir / f"{size_name}_{script_name.split('.')[0]}_output")
                
                # 特殊参数
                extra_args = []
                if script_name == 'sampler_spark.py':
                    extra_args = ['--master', 'local[*]']
                    
                result = self.run_sampler_test(script_name, input_pattern, output_prefix, extra_args)
                results[size_name][display_name] = result
                
                # 清理输出文件
                self._cleanup_output_files(output_prefix)
                
                if result['success']:
                    print(f"   ✅ 完成: {result['total_time']:.2f}s, "
                          f"{result['peak_memory_mb']:.1f}MB, "
                          f"{result['performance_metrics'].get('processing_speed', 0):.0f} 条/秒")
                else:
                    print(f"   ❌ 失败: {result.get('error', 'Unknown error')}")
                    
        return results
        
    def _cleanup_output_files(self, output_prefix: str):
        """清理输出文件"""
        try:
            # 清理JSONL文件
            for pattern in [f"{output_prefix}*.jsonl", f"{output_prefix}_spark"]:
                for file_path in Path().glob(pattern):
                    if file_path.is_file():
                        file_path.unlink()
                    elif file_path.is_dir():
                        shutil.rmtree(file_path)
                        
            # 清理记录文件
            for pattern in ["sampling_record*.json"]:
                for file_path in Path().glob(pattern):
                    file_path.unlink()
                    
        except Exception as e:
            print(f"⚠️ 清理文件失败: {e}")
            
    def analyze_results(self, results: Dict) -> Dict:
        """分析测试结果"""
        analysis = {
            'summary': {},
            'performance_comparison': {},
            'scalability_analysis': {},
            'recommendations': []
        }
        
        # 汇总统计
        for size_name, size_results in results.items():
            analysis['summary'][size_name] = {}
            
            for sampler_name, result in size_results.items():
                if result['success']:
                    analysis['summary'][size_name][sampler_name] = {
                        'time': result['total_time'],
                        'memory': result['peak_memory_mb'],
                        'speed': result['performance_metrics'].get('processing_speed', 0),
                        'cpu': result['avg_cpu_percent']
                    }
                    
        # 性能对比分析
        for size_name in results.keys():
            if size_name not in analysis['performance_comparison']:
                analysis['performance_comparison'][size_name] = {}
                
            size_results = results[size_name]
            baseline = size_results.get('原版', {})
            
            if baseline.get('success'):
                baseline_time = baseline['total_time']
                baseline_memory = baseline['peak_memory_mb']
                baseline_speed = baseline['performance_metrics'].get('processing_speed', 1)
                
                for sampler_name, result in size_results.items():
                    if result.get('success') and sampler_name != '原版':
                        time_improvement = ((baseline_time - result['total_time']) / baseline_time) * 100
                        memory_improvement = ((baseline_memory - result['peak_memory_mb']) / baseline_memory) * 100
                        speed_improvement = ((result['performance_metrics'].get('processing_speed', 0) - baseline_speed) / baseline_speed) * 100
                        
                        analysis['performance_comparison'][size_name][sampler_name] = {
                            'time_improvement_percent': time_improvement,
                            'memory_improvement_percent': memory_improvement,
                            'speed_improvement_percent': speed_improvement
                        }
                        
        # 扩展性分析
        for sampler_name in ['原版', '优化版', 'Spark版']:
            times = []
            data_sizes = []
            
            for size_name, size_results in results.items():
                if sampler_name in size_results and size_results[sampler_name].get('success'):
                    times.append(size_results[sampler_name]['total_time'])
                    data_sizes.append(size_results[sampler_name]['performance_metrics'].get('total_data', 0))
                    
            if len(times) >= 2:
                # 简单的扩展性分析
                time_ratio = times[-1] / times[0] if times[0] > 0 else float('inf')
                data_ratio = data_sizes[-1] / data_sizes[0] if data_sizes[0] > 0 else float('inf')
                scalability_score = data_ratio / time_ratio if time_ratio > 0 else 0
                
                analysis['scalability_analysis'][sampler_name] = {
                    'time_ratio': time_ratio,
                    'data_ratio': data_ratio,
                    'scalability_score': scalability_score
                }
                
        # 生成建议
        analysis['recommendations'] = self._generate_recommendations(analysis)
        
        return analysis
        
    def _generate_recommendations(self, analysis: Dict) -> List[str]:
        """生成性能建议"""
        recommendations = []
        
        # 基于性能对比生成建议
        for size_name, comparisons in analysis['performance_comparison'].items():
            for sampler_name, metrics in comparisons.items():
                if metrics['time_improvement_percent'] > 20:
                    recommendations.append(
                        f"✅ {sampler_name}在{size_name}数据集上时间性能提升{metrics['time_improvement_percent']:.1f}%"
                    )
                elif metrics['time_improvement_percent'] < -10:
                    recommendations.append(
                        f"⚠️ {sampler_name}在{size_name}数据集上时间性能下降{abs(metrics['time_improvement_percent']):.1f}%"
                    )
                    
        # 基于扩展性生成建议
        for sampler_name, scalability in analysis['scalability_analysis'].items():
            if scalability['scalability_score'] > 1.2:
                recommendations.append(f"✅ {sampler_name}具有良好的扩展性")
            elif scalability['scalability_score'] < 0.8:
                recommendations.append(f"⚠️ {sampler_name}扩展性较差，不适合大数据集")
                
        return recommendations
        
    def print_comprehensive_report(self, results: Dict, analysis: Dict):
        """打印全面测试报告"""
        print(f"\n{'='*80}")
        print("🏁 全面性能对比测试报告")
        print(f"{'='*80}")
        
        # 汇总表格
        print(f"\n📊 性能汇总表:")
        print(f"{'数据集':<15} {'采样器':<15} {'时间(s)':<10} {'内存(MB)':<12} {'速度(条/s)':<15} {'CPU(%)':<8}")
        print("-" * 80)
        
        for size_name, size_results in results.items():
            for sampler_name, result in size_results.items():
                if result.get('success'):
                    time_val = f"{result['total_time']:.2f}"
                    memory_val = f"{result['peak_memory_mb']:.1f}"
                    speed_val = f"{result['performance_metrics'].get('processing_speed', 0):.0f}"
                    cpu_val = f"{result['avg_cpu_percent']:.1f}"
                    
                    print(f"{size_name:<15} {sampler_name:<15} {time_val:<10} {memory_val:<12} {speed_val:<15} {cpu_val:<8}")
                else:
                    print(f"{size_name:<15} {sampler_name:<15} {'失败':<10} {'-':<12} {'-':<15} {'-':<8}")
                    
        # 性能改进分析
        print(f"\n📈 性能改进分析:")
        for size_name, comparisons in analysis['performance_comparison'].items():
            print(f"\n  {size_name} 数据集:")
            for sampler_name, metrics in comparisons.items():
                time_imp = metrics['time_improvement_percent']
                speed_imp = metrics['speed_improvement_percent']
                memory_imp = metrics['memory_improvement_percent']
                
                print(f"    {sampler_name}:")
                print(f"      时间: {time_imp:+.1f}% | 速度: {speed_imp:+.1f}% | 内存: {memory_imp:+.1f}%")
                
        # 扩展性分析
        print(f"\n📏 扩展性分析:")
        for sampler_name, scalability in analysis['scalability_analysis'].items():
            score = scalability['scalability_score']
            print(f"  {sampler_name}: 扩展性评分 {score:.2f}")
            
        # 建议
        print(f"\n💡 性能建议:")
        for recommendation in analysis['recommendations']:
            print(f"  {recommendation}")
            
        # 总结
        print(f"\n🎯 总结:")
        print("  - 小数据集(<50K记录): 原版脚本性能最佳")
        print("  - 中等数据集(50K-500K记录): 优化版脚本性能最佳")
        print("  - 大数据集(>500K记录): Spark版脚本扩展性最佳")
        print("  - 内存受限环境: 优先考虑原版或优化版")
        print("  - 分布式环境: 优先考虑Spark版")


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.start_time = time.time()
        self.peak_memory = 0
        self.cpu_samples = []
        self.memory_samples = []
        self.monitoring = False
        self.monitor_thread = None
        
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.start_time = time.time()
        self.peak_memory = 0
        self.cpu_samples = []
        self.memory_samples = []
        
        def monitor_loop():
            while self.monitoring:
                try:
                    memory_mb = self.process.memory_info().rss / 1024 / 1024
                    cpu_percent = self.process.cpu_percent()
                    elapsed = time.time() - self.start_time
                    
                    self.memory_samples.append((elapsed, memory_mb))
                    self.cpu_samples.append((elapsed, cpu_percent))
                    self.peak_memory = max(self.peak_memory, memory_mb)
                    
                    time.sleep(0.5)
                except:
                    break
                    
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
        
    def stop_monitoring(self) -> Dict:
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
            
        total_time = time.time() - self.start_time
        avg_cpu = sum(cpu for _, cpu in self.cpu_samples) / len(self.cpu_samples) if self.cpu_samples else 0
        
        return {
            'total_time': total_time,
            'peak_memory_mb': self.peak_memory,
            'avg_cpu_percent': avg_cpu,
            'memory_samples': self.memory_samples,
            'cpu_samples': self.cpu_samples
        }


def main():
    parser = argparse.ArgumentParser(description='🏁 全面抽样工具性能对比测试')
    parser.add_argument('--test-dir', default='benchmark_test_data',
                        help='测试数据目录')
    parser.add_argument('--small-size', nargs=2, type=int, default=[3, 10000],
                        help='小数据集: 文件数 行数 (默认: 3 10000)')
    parser.add_argument('--medium-size', nargs=2, type=int, default=[5, 50000],
                        help='中等数据集: 文件数 行数 (默认: 5 50000)')
    parser.add_argument('--large-size', nargs=2, type=int, default=[8, 100000],
                        help='大数据集: 文件数 行数 (默认: 8 100000)')
    parser.add_argument('--keep-data', action='store_true',
                        help='保留测试数据')
    parser.add_argument('--output', default='comprehensive_benchmark_results.json',
                        help='结果输出文件')
    
    args = parser.parse_args()
    
    # 创建测试数据集配置
    test_sizes = [
        ('small', args.small_size[0], args.small_size[1]),
        ('medium', args.medium_size[0], args.medium_size[1]),
        ('large', args.large_size[0], args.large_size[1])
    ]
    
    print("🏁 全面抽样工具性能对比测试")
    print("=" * 50)
    
    # 创建临时目录或使用指定目录
    if args.keep_data:
        test_data_dir = args.test_dir
        os.makedirs(test_data_dir, exist_ok=True)
    else:
        test_data_dir = tempfile.mkdtemp(prefix="sampler_benchmark_")
        
    try:
        benchmark = ComprehensiveBenchmark(test_data_dir)
        
        # 创建测试数据
        datasets = benchmark.create_test_datasets(test_sizes)
        
        # 运行基准测试
        results = benchmark.run_comprehensive_benchmark(datasets)
        
        # 分析结果
        analysis = benchmark.analyze_results(results)
        
        # 打印报告
        benchmark.print_comprehensive_report(results, analysis)
        
        # 保存详细结果
        full_results = {
            'test_config': {
                'test_sizes': test_sizes,
                'test_data_dir': test_data_dir
            },
            'results': results,
            'analysis': analysis,
            'timestamp': time.time()
        }
        
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(full_results, f, ensure_ascii=False, indent=2)
            
        print(f"\n📄 详细结果已保存到: {args.output}")
        
    finally:
        # 清理临时数据
        if not args.keep_data and os.path.exists(test_data_dir):
            shutil.rmtree(test_data_dir)
            print(f"🧹 已清理测试数据: {test_data_dir}")


if __name__ == "__main__":
    main()
