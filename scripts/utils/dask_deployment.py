#!/usr/bin/env python3
"""
🚀 Dask集群部署和管理工具
轻量级Dask集群的安装、配置和管理

功能：
1. Dask环境安装
2. 集群启动和停止
3. 健康检查和监控
4. 配置管理
"""

import os
import sys
import time
import json
import subprocess
import argparse
import signal
import threading
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import socket

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


class DaskClusterManager:
    """Dask集群管理器"""
    
    def __init__(self, config_file: str = "dask_cluster.json"):
        self.config_file = config_file
        self.config = self._load_config()
        self.processes = {}
        self.pid_file = "dask_cluster.pid"
        
    def _load_config(self) -> Dict:
        """加载集群配置"""
        default_config = {
            'scheduler': {
                'host': 'localhost',
                'port': 8786,
                'dashboard_port': 8787,
                'bokeh_port': 8788
            },
            'workers': {
                'count': os.cpu_count() or 4,
                'threads_per_worker': 2,
                'memory_limit': 'auto',
                'local_directory': './dask-worker-space'
            },
            'network': {
                'comm_retry_delay_min': '100ms',
                'comm_retry_delay_max': '20s',
                'comm_timeouts_connect': '30s',
                'comm_timeouts_tcp': '30s',
                'heartbeat_interval': '5s',
                'admin_tick_limit': '3s'
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并配置
                    default_config.update(loaded_config)
            except Exception as e:
                print(f"⚠️ 配置文件加载失败，使用默认配置: {e}")
                
        return default_config
        
    def _save_config(self):
        """保存配置"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
            
    def _check_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return True
        except OSError:
            return False
            
    def _find_available_port(self, start_port: int) -> int:
        """查找可用端口"""
        port = start_port
        while port < start_port + 100:
            if self._check_port_available(port):
                return port
            port += 1
        raise RuntimeError(f"无法找到可用端口 (起始: {start_port})")
        
    def install_dask(self) -> bool:
        """安装Dask"""
        print("📦 安装Dask...")
        
        try:
            # 检查是否已安装
            import dask
            print(f"✅ Dask已安装: {dask.__version__}")
            return True
        except ImportError:
            pass
            
        try:
            # 安装Dask完整版
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', 
                'dask[complete]', 'distributed', 'bokeh'
            ], check=True)
            
            print("✅ Dask安装成功")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Dask安装失败: {e}")
            return False
            
    def start_scheduler(self) -> bool:
        """启动调度器"""
        scheduler_config = self.config['scheduler']
        
        # 检查端口可用性
        if not self._check_port_available(scheduler_config['port']):
            new_port = self._find_available_port(scheduler_config['port'])
            print(f"⚠️ 端口 {scheduler_config['port']} 被占用，使用 {new_port}")
            scheduler_config['port'] = new_port
            
        if not self._check_port_available(scheduler_config['dashboard_port']):
            new_port = self._find_available_port(scheduler_config['dashboard_port'])
            print(f"⚠️ Dashboard端口 {scheduler_config['dashboard_port']} 被占用，使用 {new_port}")
            scheduler_config['dashboard_port'] = new_port
            
        cmd = [
            sys.executable, '-m', 'distributed.cli.dask_scheduler',
            '--host', scheduler_config['host'],
            '--port', str(scheduler_config['port']),
            '--dashboard-address', f":{scheduler_config['dashboard_port']}"
        ]
        
        print(f"🚀 启动Dask调度器...")
        print(f"   地址: {scheduler_config['host']}:{scheduler_config['port']}")
        print(f"   Dashboard: http://{scheduler_config['host']}:{scheduler_config['dashboard_port']}")
        
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['scheduler'] = process
            
            # 等待启动
            time.sleep(3)
            
            if process.poll() is None:
                print("✅ 调度器启动成功")
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"❌ 调度器启动失败:")
                print(f"stdout: {stdout}")
                print(f"stderr: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 调度器启动异常: {e}")
            return False
            
    def start_workers(self) -> bool:
        """启动工作节点"""
        worker_config = self.config['workers']
        scheduler_config = self.config['scheduler']
        
        scheduler_address = f"{scheduler_config['host']}:{scheduler_config['port']}"
        
        # 创建工作目录
        work_dir = Path(worker_config['local_directory'])
        work_dir.mkdir(exist_ok=True)
        
        print(f"🔧 启动 {worker_config['count']} 个Worker...")
        
        for i in range(worker_config['count']):
            worker_dir = work_dir / f"worker-{i}"
            worker_dir.mkdir(exist_ok=True)
            
            cmd = [
                sys.executable, '-m', 'distributed.cli.dask_worker',
                scheduler_address,
                '--nthreads', str(worker_config['threads_per_worker']),
                '--memory-limit', str(worker_config['memory_limit']),
                '--local-directory', str(worker_dir),
                '--name', f"worker-{i}"
            ]
            
            try:
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                self.processes[f'worker-{i}'] = process
                print(f"   ✅ Worker-{i} 启动")
                
            except Exception as e:
                print(f"   ❌ Worker-{i} 启动失败: {e}")
                
        # 等待Worker连接
        time.sleep(5)
        
        # 检查Worker状态
        active_workers = 0
        for i in range(worker_config['count']):
            process = self.processes.get(f'worker-{i}')
            if process and process.poll() is None:
                active_workers += 1
                
        print(f"✅ {active_workers}/{worker_config['count']} Worker启动成功")
        return active_workers > 0
        
    def start_cluster(self) -> bool:
        """启动完整集群"""
        print("🚀 启动Dask集群")
        print("=" * 50)
        
        # 启动调度器
        if not self.start_scheduler():
            return False
            
        # 启动工作节点
        if not self.start_workers():
            self.stop_cluster()
            return False
            
        # 保存进程信息
        self._save_pid_file()
        
        # 保存更新的配置
        self._save_config()
        
        print("\n🎉 Dask集群启动完成!")
        self.print_cluster_info()
        
        return True
        
    def stop_cluster(self):
        """停止集群"""
        print("🛑 停止Dask集群...")
        
        # 停止所有进程
        for name, process in self.processes.items():
            try:
                if process.poll() is None:
                    print(f"   停止 {name}...")
                    process.terminate()
                    
                    # 等待进程结束
                    try:
                        process.wait(timeout=10)
                    except subprocess.TimeoutExpired:
                        print(f"   强制终止 {name}...")
                        process.kill()
                        
            except Exception as e:
                print(f"   ⚠️ 停止 {name} 失败: {e}")
                
        self.processes.clear()
        
        # 清理PID文件
        if os.path.exists(self.pid_file):
            os.remove(self.pid_file)
            
        print("✅ 集群已停止")
        
    def _save_pid_file(self):
        """保存进程ID文件"""
        pid_data = {}
        for name, process in self.processes.items():
            if process.poll() is None:
                pid_data[name] = process.pid
                
        with open(self.pid_file, 'w') as f:
            json.dump(pid_data, f, indent=2)
            
    def _load_pid_file(self) -> Dict[str, int]:
        """加载进程ID文件"""
        if not os.path.exists(self.pid_file):
            return {}
            
        try:
            with open(self.pid_file, 'r') as f:
                return json.load(f)
        except:
            return {}
            
    def check_cluster_status(self) -> Dict:
        """检查集群状态"""
        status = {
            'scheduler': {'running': False, 'pid': None},
            'workers': {},
            'total_workers': 0,
            'active_workers': 0
        }
        
        # 从PID文件加载进程信息
        pid_data = self._load_pid_file()
        
        # 检查调度器
        if 'scheduler' in pid_data:
            pid = pid_data['scheduler']
            if PSUTIL_AVAILABLE:
                try:
                    process = psutil.Process(pid)
                    if process.is_running():
                        status['scheduler'] = {'running': True, 'pid': pid}
                except:
                    pass
                    
        # 检查Worker
        for name, pid in pid_data.items():
            if name.startswith('worker-'):
                status['total_workers'] += 1
                worker_status = {'running': False, 'pid': pid}
                
                if PSUTIL_AVAILABLE:
                    try:
                        process = psutil.Process(pid)
                        if process.is_running():
                            worker_status['running'] = True
                            status['active_workers'] += 1
                    except:
                        pass
                        
                status['workers'][name] = worker_status
                
        return status
        
    def print_cluster_info(self):
        """打印集群信息"""
        scheduler_config = self.config['scheduler']
        worker_config = self.config['workers']
        
        print(f"\n{'='*50}")
        print("📊 Dask集群信息")
        print(f"{'='*50}")
        print(f"🖥️  调度器地址: {scheduler_config['host']}:{scheduler_config['port']}")
        print(f"📊 Dashboard: http://{scheduler_config['host']}:{scheduler_config['dashboard_port']}")
        print(f"👥 Worker数量: {worker_config['count']}")
        print(f"🧵 每Worker线程: {worker_config['threads_per_worker']}")
        print(f"💾 内存限制: {worker_config['memory_limit']}")
        
        # 连接命令
        print(f"\n💡 连接命令:")
        print(f"from dask.distributed import Client")
        print(f"client = Client('{scheduler_config['host']}:{scheduler_config['port']}')")
        
    def health_check(self) -> bool:
        """健康检查"""
        print("🔍 Dask集群健康检查")
        print("=" * 40)
        
        status = self.check_cluster_status()
        
        # 检查调度器
        if status['scheduler']['running']:
            print("✅ 调度器: 运行中")
        else:
            print("❌ 调度器: 未运行")
            return False
            
        # 检查Worker
        active_workers = status['active_workers']
        total_workers = status['total_workers']
        
        print(f"👥 Worker: {active_workers}/{total_workers} 运行中")
        
        if active_workers == 0:
            print("❌ 没有活跃的Worker")
            return False
        elif active_workers < total_workers:
            print("⚠️ 部分Worker未运行")
            
        # 尝试连接测试
        try:
            from dask.distributed import Client
            scheduler_config = self.config['scheduler']
            address = f"{scheduler_config['host']}:{scheduler_config['port']}"
            
            client = Client(address, timeout='10s')
            cluster_info = client.scheduler_info()
            client.close()
            
            print(f"✅ 连接测试: 成功")
            print(f"   - 可用Worker: {len(cluster_info['workers'])}")
            print(f"   - 总CPU核心: {sum(w['nthreads'] for w in cluster_info['workers'].values())}")
            
            return True
            
        except Exception as e:
            print(f"❌ 连接测试: 失败 ({e})")
            return False


def main():
    parser = argparse.ArgumentParser(description='🚀 Dask集群部署和管理工具')
    parser.add_argument('action', choices=['install', 'start', 'stop', 'status', 'health'],
                        help='操作类型')
    parser.add_argument('--config', default='dask_cluster.json',
                        help='配置文件路径')
    parser.add_argument('--workers', type=int,
                        help='Worker数量')
    parser.add_argument('--threads', type=int, default=2,
                        help='每Worker线程数')
    parser.add_argument('--memory', default='auto',
                        help='每Worker内存限制')
    
    args = parser.parse_args()
    
    manager = DaskClusterManager(args.config)
    
    # 更新配置
    if args.workers:
        manager.config['workers']['count'] = args.workers
    if args.threads:
        manager.config['workers']['threads_per_worker'] = args.threads
    if args.memory:
        manager.config['workers']['memory_limit'] = args.memory
        
    if args.action == 'install':
        if manager.install_dask():
            print("✅ Dask安装完成")
        else:
            print("❌ Dask安装失败")
            sys.exit(1)
            
    elif args.action == 'start':
        if manager.start_cluster():
            print("\n🎉 集群启动成功!")
            print("💡 使用 'python dask_deployment.py status' 查看状态")
            print("💡 使用 'python dask_deployment.py stop' 停止集群")
        else:
            print("❌ 集群启动失败")
            sys.exit(1)
            
    elif args.action == 'stop':
        manager.stop_cluster()
        
    elif args.action == 'status':
        status = manager.check_cluster_status()
        
        print("📊 集群状态:")
        print(f"   调度器: {'运行中' if status['scheduler']['running'] else '未运行'}")
        print(f"   Worker: {status['active_workers']}/{status['total_workers']} 运行中")
        
        if status['scheduler']['running']:
            manager.print_cluster_info()
            
    elif args.action == 'health':
        if manager.health_check():
            print("\n✅ 集群健康状态良好")
        else:
            print("\n❌ 集群存在问题")
            sys.exit(1)


if __name__ == "__main__":
    main()
