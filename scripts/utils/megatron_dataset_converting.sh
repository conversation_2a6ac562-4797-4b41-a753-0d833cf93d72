#!/bin/bash

# 本脚本用于将指定目录下的所有 jsonl 文件转换为 megatron 数据格式

set -euo pipefail  # 错误时退出，未定义变量报错，管道错误传播

# 参数检查
if [[ $# -lt 2 ]]; then
    echo "用法: $0 <work_dir> <batch> <prefix> [workers] [n_subs]" >&2
    echo "示例: $0 /path/to/data batch1 my_dataset 32 32" >&2
    exit 1
fi

# 参数设置
readonly work_dir="$1"
readonly batch="$2"
readonly prefix="$3"
readonly workers="${4:-50}"
readonly n_subs="${5:-10}"

# 配置常量
readonly tokenizer_name_or_path="/home/<USER>/qwen/Qwen3-4B-Base"
readonly output_base_path="/home/<USER>/datasets/megatron/datasets"
readonly python_script="/home/<USER>/wangzixian/Unicom3/MindSpeed-LLM/preprocess_data.py"

# 验证输入目录
if [[ ! -d "$work_dir" ]]; then
    echo "错误: 工作目录 '$work_dir' 不存在" >&2
    exit 1
fi

# 验证 Python 脚本存在
if [[ ! -f "$python_script" ]]; then
    echo "错误: Python 脚本 '$python_script' 不存在" >&2
    exit 1
fi

# 验证 tokenizer 路径
if [[ ! -d "$tokenizer_name_or_path" ]]; then
    echo "警告: tokenizer 路径 '$tokenizer_name_or_path' 不存在"
fi

# 创建输出目录
mkdir -p "$output_base_path"

# 处理函数
process_batch() {
    local output_prefix="${output_base_path}/${prefix}_${batch}"

    # 检查目录是否包含 jsonl 文件
    if ! find "$work_dir" -name "*.jsonl" -type f | head -1 | grep -q .; then
        echo "警告: $work_dir 中未找到 jsonl 文件，跳过"
        return 0
    fi

    # 执行处理命令
    if python "$python_script" \
        --input "$work_dir" \
        --tokenizer-name-or-path "$tokenizer_name_or_path" \
        --tokenizer-type PretrainedFromHF \
        --handler-name GeneralPretrainHandler \
        --output-prefix "$output_prefix" \
        --json-keys text \
        --workers "$workers" \
        --n-subs "$n_subs" \
        --log-interval 1000 \
        --append-eod; then
        echo " ✓ 成功处理: $work_dir"
    else
        echo " ✗ 处理失败: $work_dir" >&2
        return 1
    fi
}

# 开始处理
echo "开始处理 $work_dir 下的 jsonl 文件..."
echo "配置: workers=$workers, n_subs=$n_subs"
echo "输出路径: $output_base_path"
echo "----------------------------------------"

process_batch
