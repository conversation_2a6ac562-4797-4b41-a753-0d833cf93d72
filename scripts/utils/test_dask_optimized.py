#!/usr/bin/env python3
"""
🔧 优化的Dask测试脚本
测试修复GIL阻塞问题后的Dask配置
"""

import os
import sys
import time
import json
import tempfile
from pathlib import Path

try:
    import dask
    import dask.bag as db
    from dask.distributed import Client
    print(f"✅ Dask导入成功: {dask.__version__}")
except ImportError as e:
    print(f"❌ Dask未安装: {e}")
    sys.exit(1)


def test_optimized_dask():
    """测试优化的Dask配置"""
    print("🔧 测试优化的Dask配置...")
    
    # 设置优化配置避免GIL阻塞
    dask.config.set({
        'distributed.worker.daemon': False,
        'distributed.admin.tick.limit': '10s',  # 增加tick限制
        'distributed.comm.timeouts.connect': '30s',
        'distributed.worker.memory.target': 0.6,
        'array.chunk-size': '32MB',  # 较小的chunk
        'distributed.comm.compression': 'lz4',  # 启用压缩
        'distributed.worker.multiprocessing-method': 'spawn',  # 使用spawn
    })
    
    try:
        # 使用优化的客户端配置
        with Client(
            processes=True,  # 使用进程避免GIL
            threads_per_worker=1,  # 每个worker只用1个线程
            n_workers=2,
            memory_limit='2GB',
            silence_logs=False,
            dashboard_address=':8787',
            heartbeat_interval='5s',  # 增加心跳间隔
            death_timeout='60s'  # 增加死亡超时
        ) as client:
            print("✅ 优化的Dask客户端创建成功")
            print(f"   Dashboard: {client.dashboard_link}")
            
            # 等待Worker就绪
            client.wait_for_workers(n_workers=2, timeout=30)
            
            # 测试小数据处理
            print("🔄 测试小数据处理...")
            data = list(range(1000))
            bag = db.from_sequence(data, partition_size=100)  # 小分区
            result = bag.map(lambda x: x * 2).sum().compute()
            expected = sum(x * 2 for x in data)
            
            if result == expected:
                print(f"✅ 小数据测试通过: {result}")
            else:
                print(f"❌ 小数据测试失败: {result} != {expected}")
                return False
                
            # 测试文本处理
            print("🔄 测试文本处理...")
            text_data = [f"line_{i}" for i in range(2000)]
            text_bag = db.from_sequence(text_data, partition_size=200)  # 小分区
            processed = text_bag.map(lambda x: x.upper()).take(5)
            
            if len(processed) == 5 and processed[0] == "LINE_0":
                print("✅ 文本处理测试通过")
            else:
                print("❌ 文本处理测试失败")
                return False
                
            return True
                
    except Exception as e:
        print(f"❌ 优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_jsonl_with_optimization():
    """测试优化的JSONL处理"""
    print("📄 测试优化的JSONL处理...")
    
    # 创建较大的测试数据
    test_data = []
    for i in range(2000):  # 增加数据量
        test_data.append({
            'id': f'test_{i:04d}',
            'source': f'source_{i % 5}',  # 5个source
            'content': f'测试内容 {i} - ' + 'x' * (i % 20),
            'value': i
        })
    
    # 写入临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
        for item in test_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
        temp_file = f.name
    
    try:
        with Client(
            processes=True, 
            threads_per_worker=1, 
            n_workers=2, 
            memory_limit='2GB',
            silence_logs=False,
            dashboard_address=None,
            heartbeat_interval='5s',
            death_timeout='60s'
        ) as client:
            print("✅ 客户端创建成功")
            
            # 读取文件 - 使用小块大小
            print("📖 读取JSONL文件...")
            bag = db.read_text(temp_file, blocksize='8MB')  # 小块大小
            
            # 解析JSON
            def parse_json_safe(line):
                line = line.strip()
                if not line:
                    return None
                try:
                    return json.loads(line)
                except:
                    return None
            
            parsed_bag = bag.map(parse_json_safe).filter(lambda x: x is not None)
            
            # 重新分区为小分区
            parsed_bag = parsed_bag.repartition(partition_size=200)
            
            # 统计
            print("📊 计算统计信息...")
            total_count = parsed_bag.count().compute()
            print(f"📊 总记录数: {total_count}")
            
            # 按source分组统计
            source_counts = parsed_bag.pluck('source').frequencies().compute()
            print(f"📈 Source分布: {dict(source_counts)}")
            
            # 采样测试
            sample_ratio = 0.2
            sample_count = max(1, int(total_count * sample_ratio))
            sampled_data = parsed_bag.take(sample_count)
            
            print(f"🎯 采样结果: {len(sampled_data)}/{total_count} 条")
            
            if len(sampled_data) > 0 and total_count == len(test_data):
                print("✅ 优化的JSONL处理测试通过")
                return True
            else:
                print("❌ 优化的JSONL处理测试失败")
                return False
                
    except Exception as e:
        print(f"❌ 优化的JSONL测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            os.unlink(temp_file)
        except:
            pass


def main():
    print("🔧 Dask GIL阻塞优化测试")
    print("=" * 40)
    
    # 测试优化配置
    if not test_optimized_dask():
        print("❌ 优化配置测试失败")
        return
    
    # 测试优化的JSONL处理
    if not test_jsonl_with_optimization():
        print("❌ 优化的JSONL测试失败")
        return
    
    print("\n🎉 所有优化测试通过!")
    print("✅ Dask GIL阻塞问题已修复")
    print("💡 现在可以安全使用优化的 sampler_dask.py")
    
    print("\n💡 优化要点:")
    print("1. 使用进程而不是线程避免GIL")
    print("2. 减小chunk和分区大小")
    print("3. 增加超时限制")
    print("4. 启用压缩减少传输时间")
    print("5. 使用spawn方法启动进程")


if __name__ == "__main__":
    main()
