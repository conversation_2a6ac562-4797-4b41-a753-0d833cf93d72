#!/usr/bin/env python3
"""
🚀 基于Dask的分布式JSONL文件抽样工具
Python原生分布式计算，轻量级高性能

特性：
1. Python原生分布式处理
2. 动态任务图优化
3. 内存友好的数据处理
4. 与Pandas/NumPy完美集成
5. 轻量级部署和配置
"""

import os
import sys
import threading
import time
import json
import argparse
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

try:
    import dask
    import dask.bag as db
    import dask.dataframe as dd
    from dask.distributed import Client, as_completed, progress
    from dask.diagnostics import ProgressBar
    import pandas as pd
    import numpy as np
    DASK_AVAILABLE = True
except ImportError:
    DASK_AVAILABLE = False
    print("❌ Dask未安装，请运行: pip install dask[complete]")
    sys.exit(1)

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


class DaskSamplerConfig:
    """Dask采样器配置"""
    
    def __init__(self):
        # 集群配置
        self.scheduler_address = None  # None表示本地模式
        self.n_workers = None  # 自动检测
        self.threads_per_worker = 2
        self.memory_limit = 'auto'
        
        # 性能配置
        self.chunk_size = '128MB'
        self.optimize_graph = True
        self.cache_intermediate = True
        
        # 网络配置
        self.comm_retry_delay = '1s'
        self.comm_timeouts = '30s'
        self.heartbeat_interval = '5s'
        
        # 内存管理
        self.memory_target_fraction = 0.6
        self.memory_spill_fraction = 0.7
        self.memory_pause_fraction = 0.8
        
    def get_client_config(self) -> Dict:
        """获取Client配置"""
        config = {}
        
        if self.scheduler_address:
            config['address'] = self.scheduler_address
        else:
            # 本地模式配置
            config['n_workers'] = self.n_workers or os.cpu_count()
            config['threads_per_worker'] = self.threads_per_worker
            config['memory_limit'] = self.memory_limit
            
        return config
        
    def apply_dask_config(self):
        """应用Dask全局配置"""
        dask.config.set({
            'dataframe.query-planning': False,  # 使用传统查询规划
            'optimization.fuse': {},  # 启用图优化
            'array.chunk-size': self.chunk_size,
            'distributed.comm.retry.delay': self.comm_retry_delay,
            'distributed.comm.timeouts.connect': self.comm_timeouts,
            'distributed.worker.memory.target': self.memory_target_fraction,
            'distributed.worker.memory.spill': self.memory_spill_fraction,
            'distributed.worker.memory.pause': self.memory_pause_fraction,
        })


class DaskPerformanceMonitor:
    """Dask性能监控器"""

    def __init__(self, client: Optional[Client] = None):
        self.client = client
        self.start_time = time.time()
        self.checkpoints = {}
        self.task_stats = []
        self.resource_history = []
        self.monitoring_thread = None
        self.monitoring_active = False

    def start_monitoring(self):
        """开始资源监控"""
        if not self.client:
            return

        self.monitoring_active = True

        def monitor_resources():
            while self.monitoring_active:
                try:
                    # 获取集群资源使用情况
                    info = self.client.scheduler_info()
                    workers = info.get('workers', {})

                    total_memory_used = 0
                    total_memory_limit = 0
                    total_cpu_usage = 0
                    active_tasks = 0

                    for worker_info in workers.values():
                        # 内存使用
                        memory_used = worker_info.get('memory', 0)
                        memory_limit = worker_info.get('memory_limit', 0)
                        total_memory_used += memory_used
                        total_memory_limit += memory_limit

                        # CPU使用 (近似)
                        total_cpu_usage += len(worker_info.get('processing', {}))

                        # 活跃任务
                        active_tasks += len(worker_info.get('processing', {}))

                    resource_snapshot = {
                        'timestamp': time.time(),
                        'memory_used_gb': total_memory_used / (1024**3),
                        'memory_limit_gb': total_memory_limit / (1024**3),
                        'memory_usage_percent': (total_memory_used / total_memory_limit * 100) if total_memory_limit > 0 else 0,
                        'active_tasks': active_tasks,
                        'worker_count': len(workers)
                    }

                    self.resource_history.append(resource_snapshot)

                    # 保留最近100个采样点
                    if len(self.resource_history) > 100:
                        self.resource_history.pop(0)

                except Exception as e:
                    pass  # 忽略监控错误

                time.sleep(2)  # 每2秒采样一次

        self.monitoring_thread = threading.Thread(target=monitor_resources, daemon=True)
        self.monitoring_thread.start()

    def stop_monitoring(self):
        """停止资源监控"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=1)

    def checkpoint(self, name: str):
        """记录检查点"""
        current_time = time.time()
        elapsed = current_time - self.start_time

        cluster_info = {}
        if self.client:
            try:
                info = self.client.scheduler_info()
                workers = info.get('workers', {})
                cluster_info = {
                    'workers': len(workers),
                    'total_cores': sum(w.get('nthreads', 0) for w in workers.values()),
                    'total_memory_gb': sum(w.get('memory_limit', 0) for w in workers.values()) / (1024**3),
                    'tasks_processing': sum(len(w.get('processing', {})) for w in workers.values()),
                    'dashboard_link': getattr(self.client, 'dashboard_link', 'N/A')
                }
            except:
                pass

        self.checkpoints[name] = {
            'time': current_time,
            'elapsed': elapsed,
            'cluster_info': cluster_info
        }

    def get_cluster_stats(self) -> Dict:
        """获取集群统计信息"""
        if not self.client:
            return {'mode': 'local', 'workers': 1}

        try:
            info = self.client.scheduler_info()
            workers = info.get('workers', {})

            return {
                'mode': 'distributed',
                'workers': len(workers),
                'total_cores': sum(w.get('nthreads', 0) for w in workers.values()),
                'total_memory_gb': sum(w.get('memory_limit', 0) for w in workers.values()) / (1024**3),
                'scheduler_address': info.get('address', 'unknown'),
                'dashboard_link': getattr(self.client, 'dashboard_link', 'N/A')
            }
        except:
            return {'mode': 'unknown', 'workers': 0}

    def get_resource_summary(self) -> Dict:
        """获取资源使用摘要"""
        if not self.resource_history:
            return {}

        # 计算平均值和峰值
        memory_usage = [r['memory_usage_percent'] for r in self.resource_history]
        active_tasks = [r['active_tasks'] for r in self.resource_history]

        return {
            'avg_memory_usage_percent': sum(memory_usage) / len(memory_usage),
            'peak_memory_usage_percent': max(memory_usage),
            'avg_active_tasks': sum(active_tasks) / len(active_tasks),
            'peak_active_tasks': max(active_tasks),
            'total_samples': len(self.resource_history)
        }

    def print_summary(self):
        """打印性能摘要"""
        total_time = time.time() - self.start_time
        cluster_stats = self.get_cluster_stats()
        resource_summary = self.get_resource_summary()

        print(f"\n{'='*60}")
        print("🚀 Dask性能分析报告")
        print(f"{'='*60}")
        print(f"⏱️  总耗时: {total_time:.2f}秒")
        print(f"🖥️  集群信息:")
        print(f"   - 模式: {cluster_stats['mode']}")
        print(f"   - Worker数量: {cluster_stats['workers']}")
        if 'total_cores' in cluster_stats:
            print(f"   - 总CPU核心: {cluster_stats['total_cores']}")
        if 'total_memory_gb' in cluster_stats:
            print(f"   - 总内存: {cluster_stats['total_memory_gb']:.1f}GB")
        if 'dashboard_link' in cluster_stats and cluster_stats['dashboard_link'] != 'N/A':
            print(f"   - Dashboard: {cluster_stats['dashboard_link']}")

        # 资源使用摘要
        if resource_summary:
            print(f"\n📊 资源使用摘要:")
            print(f"   - 平均内存使用: {resource_summary['avg_memory_usage_percent']:.1f}%")
            print(f"   - 峰值内存使用: {resource_summary['peak_memory_usage_percent']:.1f}%")
            print(f"   - 平均活跃任务: {resource_summary['avg_active_tasks']:.1f}")
            print(f"   - 峰值活跃任务: {resource_summary['peak_active_tasks']}")

        if len(self.checkpoints) > 1:
            print(f"\n📈 各阶段耗时:")
            prev_time = self.start_time
            for name, data in self.checkpoints.items():
                stage_time = data['time'] - prev_time
                percentage = (stage_time / total_time) * 100
                print(f"  - {name}: {stage_time:.2f}s ({percentage:.1f}%)")
                prev_time = data['time']


class DaskJSONLSampler:
    """Dask分布式JSONL采样器"""
    
    def __init__(self, config: DaskSamplerConfig):
        self.config = config
        self.client = None
        self.monitor = None
        
    def __enter__(self):
        """上下文管理器入口"""
        # 应用Dask配置
        self.config.apply_dask_config()

        # 创建客户端
        client_config = self.config.get_client_config()

        try:
            if self.config.scheduler_address:
                self.client = Client(self.config.scheduler_address)
                print(f"✅ 连接到Dask集群: {self.config.scheduler_address}")
            else:
                self.client = Client(**client_config)
                print(f"✅ 启动本地Dask集群")

            # 获取集群信息
            scheduler_info = self.client.scheduler_info()
            workers = scheduler_info.get('workers', {})
            print(f"   - Workers: {len(workers)}")
            print(f"   - 总CPU核心: {sum(w.get('nthreads', 0) for w in workers.values())}")
            print(f"   - 总内存: {sum(w.get('memory_limit', 0) for w in workers.values()) / (1024**3):.1f}GB")
            print(f"   - Dashboard: {self.client.dashboard_link}")

        except Exception as e:
            print(f"⚠️ 无法创建Dask客户端，使用单线程模式: {e}")
            self.client = None

        self.monitor = DaskPerformanceMonitor(self.client)

        # 启动资源监控
        if self.client:
            self.monitor.start_monitoring()
            print("📊 已启动资源监控")

        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        # 停止监控
        if self.monitor:
            self.monitor.stop_monitoring()

        if self.client:
            self.client.close()
            print("✅ Dask客户端已关闭")
            
    def read_jsonl_files(self, file_pattern: str) -> db.Bag:
        """读取JSONL文件为Dask Bag"""
        self.monitor.checkpoint("开始读取文件")
        
        print(f"📁 读取文件模式: {file_pattern}")
        
        # 使用Dask Bag读取JSON Lines
        try:
            # 读取文本文件
            bag = db.read_text(file_pattern, blocksize=self.config.chunk_size)
            
            # 解析JSON
            def parse_json_safe(line):
                line = line.strip()
                if not line:
                    return None
                try:
                    data = json.loads(line)
                    if 'source' in data:
                        return data
                    return None
                except:
                    return None
                    
            bag = bag.map(parse_json_safe).filter(lambda x: x is not None)
            
            # 计算总数
            print("📊 计算数据量...")
            total_count = bag.count().compute()
            print(f"📊 总记录数: {total_count:,}")
            
            # 显示source分布
            print("📈 分析Source分布...")
            source_counts = bag.pluck('source').frequencies().compute()
            print(f"📈 Source分布:")
            for source, count in sorted(source_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"   - {source}: {count:,} 条")
                
            self.monitor.checkpoint("文件读取完成")
            return bag
            
        except Exception as e:
            print(f"❌ 文件读取失败: {e}")
            raise
            
    def load_previous_records(self, record_files: List[str]) -> Dict[str, set]:
        """加载历史采样记录"""
        if not record_files:
            return {}
            
        print("📚 加载历史采样记录...")
        sampled_indices = {}
        
        for record_file in record_files:
            if not os.path.exists(record_file):
                continue
                
            try:
                with open(record_file, 'r', encoding='utf-8') as f:
                    record_data = json.load(f)
                    
                if 'source_details' in record_data:
                    for source, indices in record_data['source_details'].items():
                        if source not in sampled_indices:
                            sampled_indices[source] = set()
                        sampled_indices[source].update(indices)
                        
            except Exception as e:
                print(f"⚠️ 记录文件 {record_file} 加载失败: {e}")
                
        if sampled_indices:
            total_previous = sum(len(indices) for indices in sampled_indices.values())
            print(f"📋 加载了 {total_previous:,} 条历史采样记录")
            
        return sampled_indices
        
    def filter_previous_samples(self, bag: db.Bag, previous_records: Dict[str, set]) -> db.Bag:
        """过滤已采样的数据"""
        if not previous_records:
            return bag
            
        self.monitor.checkpoint("开始过滤历史数据")
        
        def add_row_id(partition):
            """为每个分区添加行号"""
            result = []
            for i, item in enumerate(partition):
                item['_row_id'] = i
                result.append(item)
            return result
            
        def filter_sampled(partition):
            """过滤已采样的数据"""
            result = []
            for item in partition:
                source = item['source']
                row_id = item['_row_id']
                
                if source not in previous_records or row_id not in previous_records[source]:
                    # 移除临时的row_id
                    del item['_row_id']
                    result.append(item)
                    
            return result
            
        # 添加行号并过滤
        filtered_bag = bag.map_partitions(add_row_id).map_partitions(filter_sampled)
        
        remaining_count = filtered_bag.count().compute()
        print(f"📊 过滤后剩余: {remaining_count:,} 条记录")
        
        self.monitor.checkpoint("历史数据过滤完成")
        return filtered_bag
        
    def stratified_sample(self, bag: db.Bag, sample_ratio: float, 
                         random_seed: int = 42) -> Tuple[List[dict], Dict[str, int]]:
        """分层采样"""
        self.monitor.checkpoint("开始分层采样")
        
        print(f"🎯 开始分层采样 (比例: {sample_ratio*100:.1f}%)")
        
        # 按source分组
        grouped = bag.groupby('source')
        
        # 计算每个source的采样数量
        source_counts = bag.pluck('source').frequencies().compute()
        sampling_plan = {}
        
        print(f"📋 采样计划:")
        for source, total_count in source_counts.items():
            sample_count = max(1, int(total_count * sample_ratio))
            sample_count = min(sample_count, total_count)
            sampling_plan[source] = sample_count
            print(f"   - {source}: {total_count:,} -> {sample_count:,}")
            
        # 执行分层采样
        def sample_group(group_data):
            """对单个组进行采样"""
            source, items = group_data
            items_list = list(items)
            
            if source not in sampling_plan:
                return []
                
            sample_count = sampling_plan[source]
            if sample_count >= len(items_list):
                return items_list
                
            # 随机采样
            np.random.seed(random_seed)
            indices = np.random.choice(len(items_list), sample_count, replace=False)
            return [items_list[i] for i in sorted(indices)]
            
        # 执行采样
        sampled_bag = grouped.map(sample_group).flatten()
        
        # 收集结果
        print("📥 收集采样结果...")
        if self.client:
            with ProgressBar():
                sampled_data = sampled_bag.compute()
        else:
            sampled_data = sampled_bag.compute()
            
        total_sampled = len(sampled_data)
        print(f"✅ 采样完成，共 {total_sampled:,} 条数据")
        
        # 统计实际采样数量
        actual_counts = {}
        for item in sampled_data:
            source = item['source']
            actual_counts[source] = actual_counts.get(source, 0) + 1
            
        self.monitor.checkpoint("分层采样完成")
        return sampled_data, actual_counts
        
    def write_results(self, sampled_data: List[dict], output_prefix: str,
                     max_lines_per_file: int = 10000) -> List[str]:
        """写入采样结果"""
        self.monitor.checkpoint("开始写入结果")
        
        total_count = len(sampled_data)
        if total_count == 0:
            return []
            
        num_files = max(1, (total_count + max_lines_per_file - 1) // max_lines_per_file)
        
        print(f"💾 写入 {total_count:,} 条记录到 {num_files} 个文件")
        
        output_files = []
        
        for file_idx in range(num_files):
            start_idx = file_idx * max_lines_per_file
            end_idx = min(start_idx + max_lines_per_file, total_count)
            
            if num_files == 1:
                output_file = f"{output_prefix}.jsonl"
            else:
                output_file = f"{output_prefix}_part{file_idx + 1:03d}.jsonl"
                
            # 写入文件
            with open(output_file, 'w', encoding='utf-8') as f:
                for item in sampled_data[start_idx:end_idx]:
                    f.write(json.dumps(item, ensure_ascii=False) + '\n')
                    
            output_files.append(output_file)
            
        print(f"✅ 结果已写入 {len(output_files)} 个文件")
        
        self.monitor.checkpoint("结果写入完成")
        return output_files
        
    def save_sampling_record(self, sample_details: Dict[str, int], output_files: List[str],
                           record_file: str, sample_ratio: float, random_seed: int):
        """保存采样记录"""
        cluster_stats = self.monitor.get_cluster_stats()
        
        record_data = {
            'timestamp': datetime.now().isoformat(),
            'sample_ratio': sample_ratio,
            'random_seed': random_seed,
            'total_sampled': sum(sample_details.values()),
            'output_files': output_files,
            'source_details': sample_details,
            'dask_config': {
                'mode': cluster_stats['mode'],
                'workers': cluster_stats['workers'],
                'scheduler_address': self.config.scheduler_address,
                'chunk_size': self.config.chunk_size
            }
        }
        
        with open(record_file, 'w', encoding='utf-8') as f:
            json.dump(record_data, f, ensure_ascii=False, indent=2)
            
        print(f"📄 采样记录已保存到: {record_file}")


def main():
    parser = argparse.ArgumentParser(description='🚀 Dask分布式JSONL文件采样工具')
    parser.add_argument('--input', '-i', default='*.jsonl',
                        help='输入文件匹配模式 (默认: *.jsonl)')
    parser.add_argument('--output', '-o', default='sampled_data',
                        help='输出文件前缀 (默认: sampled_data)')
    parser.add_argument('--ratio', '-r', type=float, default=0.1,
                        help='抽样比例 (默认: 0.1 即10%%)')
    parser.add_argument('--seed', '-s', type=int, default=42,
                        help='随机种子 (默认: 42)')
    parser.add_argument('--record', default='sampling_record_dask.json',
                        help='本次索引记录文件名')
    parser.add_argument('--previous-records', nargs='*', default=[],
                        help='之前的采样记录文件路径列表')
    parser.add_argument('--max-lines', type=int, default=10000,
                        help='每个输出文件的最大行数 (默认: 10,000)')
    
    # Dask配置参数
    parser.add_argument('--scheduler', 
                        help='Dask调度器地址 (如: tcp://localhost:8786)')
    parser.add_argument('--n-workers', type=int,
                        help='Worker数量 (默认: CPU核心数)')
    parser.add_argument('--threads-per-worker', type=int, default=2,
                        help='每个Worker的线程数 (默认: 2)')
    parser.add_argument('--memory-limit', default='auto',
                        help='每个Worker的内存限制 (默认: auto)')
    parser.add_argument('--chunk-size', default='128MB',
                        help='数据块大小 (默认: 128MB)')
    
    args = parser.parse_args()
    
    if not 0 < args.ratio <= 1.0:
        print("❌ 错误: 抽样比例必须在 0 和 1 之间")
        return
        
    # 创建Dask配置
    config = DaskSamplerConfig()
    config.scheduler_address = args.scheduler
    config.n_workers = args.n_workers
    config.threads_per_worker = args.threads_per_worker
    config.memory_limit = args.memory_limit
    config.chunk_size = args.chunk_size
    
    start_time = time.time()
    
    try:
        with DaskJSONLSampler(config) as sampler:
            # 读取数据
            print("=" * 60)
            print("🚀 第一步: Dask分布式读取")
            print("=" * 60)
            bag = sampler.read_jsonl_files(args.input)
            
            # 加载历史记录
            print("\n" + "=" * 60)
            print("📚 第二步: 加载历史记录")
            print("=" * 60)
            previous_records = sampler.load_previous_records(args.previous_records)
            
            # 过滤已采样数据
            print("\n" + "=" * 60)
            print("🔍 第三步: 过滤历史数据")
            print("=" * 60)
            filtered_bag = sampler.filter_previous_samples(bag, previous_records)
            
            # 分层采样
            print("\n" + "=" * 60)
            print("🎯 第四步: Dask分布式采样")
            print("=" * 60)
            sampled_data, sample_details = sampler.stratified_sample(
                filtered_bag, args.ratio, args.seed
            )
            
            # 写入结果
            print("\n" + "=" * 60)
            print("💾 第五步: 写入结果")
            print("=" * 60)
            output_files = sampler.write_results(sampled_data, args.output, args.max_lines)
            
            # 保存记录
            sampler.save_sampling_record(
                sample_details, output_files, args.record, args.ratio, args.seed
            )
            
            # 性能报告
            total_time = time.time() - start_time
            total_sampled = sum(sample_details.values())
            original_count = bag.count().compute() if 'bag' in locals() else 0
            
            print("\n" + "=" * 60)
            print("🎉 Dask分布式采样完成!")
            print("=" * 60)
            print(f"⏱️  总耗时: {total_time:.2f}秒")
            if original_count > 0:
                print(f"🔥 处理速度: {original_count / total_time:.0f} 条/秒")
            print(f"📊 原始数据: {original_count:,} 条")
            print(f"📈 本次采样: {total_sampled:,} 条")
            print(f"📁 输出文件: {len(output_files)} 个")
            
            # 打印性能摘要
            sampler.monitor.print_summary()
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
