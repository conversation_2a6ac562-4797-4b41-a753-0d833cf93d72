#!/bin/bash
# speed_test.sh - 一键测试脚本

MODEL_TYPE=${1:-"ds"}
API_KEY=${2:-""}

test_speed() {
    echo "🚀 开始测试 $1..."
    start=$(date +%s.%N)

    case $1 in
        "ds")
            curl -s -H "Content-Type: application/json" \
                 -H "Authorization: Bearer $API_KEY" \
                 -d '{"model":"DeepSeek-V3-0324","messages":[{"role":"user","content":"写个1000字的技术教程"}],"max_tokens":1200}' \
                 http://*************:8008/v1/chat/completions
            ;;
        "local")
            curl -s -H "Content-Type: application/json" \
                 -d '{"model":"llama2","prompt":"写个1000字的技术教程","options":{"num_predict":1000}}' \
                 http://localhost:11434/api/generate
            ;;
    esac

    end=$(date +%s.%N)
    duration=$(echo "$end - $start" | bc)
    echo "⏱️  耗时: ${duration}s"
}

# 使用方法
# ./speed_test.sh ds your_api_key
# ./speed_test.sh local

test_speed $MODEL_TYPE