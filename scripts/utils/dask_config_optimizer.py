#!/usr/bin/env python3
"""
🔧 Dask配置优化器
自动分析系统资源并生成最优Dask配置

功能：
1. 系统资源分析
2. 数据规模评估
3. 智能配置推荐
4. 性能调优建议
"""

import os
import sys
import json
import argparse
import glob
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import math

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("⚠️ psutil未安装，系统资源分析功能受限")


class DaskConfigOptimizer:
    """Dask配置优化器"""
    
    def __init__(self):
        self.system_info = self._analyze_system()
        self.data_info = {}
        
    def _analyze_system(self) -> Dict:
        """分析系统资源"""
        info = {
            'cpu_count': os.cpu_count() or 4,
            'memory_gb': 8.0,  # 默认值
            'disk_space_gb': 100.0,  # 默认值
            'platform': sys.platform
        }
        
        if PSUTIL_AVAILABLE:
            try:
                # CPU信息
                info['cpu_count'] = psutil.cpu_count(logical=True)
                info['cpu_count_physical'] = psutil.cpu_count(logical=False)
                info['cpu_freq'] = psutil.cpu_freq()._asdict() if psutil.cpu_freq() else {}
                
                # 内存信息
                memory = psutil.virtual_memory()
                info['memory_gb'] = memory.total / (1024**3)
                info['memory_available_gb'] = memory.available / (1024**3)
                info['memory_percent'] = memory.percent
                
                # 磁盘信息
                disk = psutil.disk_usage('/')
                info['disk_space_gb'] = disk.total / (1024**3)
                info['disk_free_gb'] = disk.free / (1024**3)
                info['disk_percent'] = (disk.used / disk.total) * 100
                
                # 网络信息
                info['network_interfaces'] = len(psutil.net_if_addrs())
                
            except Exception as e:
                print(f"⚠️ 系统信息获取部分失败: {e}")
                
        return info
        
    def analyze_data_pattern(self, pattern: str) -> Dict:
        """分析数据模式"""
        print(f"📊 分析数据模式: {pattern}")
        
        files = glob.glob(pattern)
        if not files:
            print(f"⚠️ 未找到匹配的文件: {pattern}")
            return {}
            
        total_size = 0
        total_lines = 0
        file_count = len(files)
        
        # 采样分析前几个文件
        sample_files = files[:min(5, len(files))]
        
        for file_path in sample_files:
            try:
                file_size = os.path.getsize(file_path)
                total_size += file_size
                
                # 估算行数（采样前1000行）
                with open(file_path, 'r', encoding='utf-8') as f:
                    sample_lines = []
                    for i, line in enumerate(f):
                        if i >= 1000:
                            break
                        sample_lines.append(len(line.encode('utf-8')))
                        
                    if sample_lines:
                        avg_line_size = sum(sample_lines) / len(sample_lines)
                        estimated_lines = file_size / avg_line_size
                        total_lines += estimated_lines
                        
            except Exception as e:
                print(f"⚠️ 文件分析失败 {file_path}: {e}")
                
        # 估算所有文件的总大小和行数
        if sample_files:
            avg_file_size = total_size / len(sample_files)
            avg_file_lines = total_lines / len(sample_files)
            
            estimated_total_size = avg_file_size * file_count
            estimated_total_lines = avg_file_lines * file_count
        else:
            estimated_total_size = 0
            estimated_total_lines = 0
            
        self.data_info = {
            'file_count': file_count,
            'total_size_gb': estimated_total_size / (1024**3),
            'total_lines': int(estimated_total_lines),
            'avg_file_size_mb': (estimated_total_size / file_count) / (1024**2) if file_count > 0 else 0,
            'avg_lines_per_file': int(estimated_total_lines / file_count) if file_count > 0 else 0
        }
        
        print(f"📈 数据分析结果:")
        print(f"   - 文件数量: {self.data_info['file_count']:,}")
        print(f"   - 总大小: {self.data_info['total_size_gb']:.2f}GB")
        print(f"   - 总行数: {self.data_info['total_lines']:,}")
        print(f"   - 平均文件大小: {self.data_info['avg_file_size_mb']:.1f}MB")
        
        return self.data_info
        
    def generate_optimal_config(self) -> Dict:
        """生成最优配置"""
        config = {
            'cluster': {},
            'performance': {},
            'memory': {},
            'network': {},
            'recommendations': []
        }
        
        # 集群配置
        cpu_count = self.system_info['cpu_count']
        memory_gb = self.system_info['memory_gb']
        data_size_gb = self.data_info.get('total_size_gb', 1.0)
        
        # Worker数量计算
        if data_size_gb < 1.0:
            # 小数据集
            n_workers = min(2, cpu_count // 2)
            threads_per_worker = 2
        elif data_size_gb < 10.0:
            # 中等数据集
            n_workers = min(4, cpu_count)
            threads_per_worker = max(1, cpu_count // n_workers)
        else:
            # 大数据集
            n_workers = cpu_count
            threads_per_worker = 1
            
        config['cluster'] = {
            'n_workers': n_workers,
            'threads_per_worker': threads_per_worker,
            'memory_limit': f"{int(memory_gb * 0.8 / n_workers)}GB"
        }
        
        # 性能配置
        if data_size_gb < 0.5:
            chunk_size = '64MB'
        elif data_size_gb < 5.0:
            chunk_size = '128MB'
        else:
            chunk_size = '256MB'
            
        config['performance'] = {
            'chunk_size': chunk_size,
            'optimize_graph': True,
            'cache_intermediate': data_size_gb < memory_gb * 0.5,
            'compression': data_size_gb > 1.0
        }
        
        # 内存配置
        config['memory'] = {
            'target_fraction': 0.6,
            'spill_fraction': 0.7,
            'pause_fraction': 0.8,
            'terminate_fraction': 0.95
        }
        
        # 网络配置
        config['network'] = {
            'comm_retry_delay': '1s',
            'comm_timeouts': '30s',
            'heartbeat_interval': '5s',
            'tcp_timeout': '30s'
        }
        
        # 生成建议
        config['recommendations'] = self._generate_recommendations()
        
        return config
        
    def _generate_recommendations(self) -> List[str]:
        """生成配置建议"""
        recommendations = []
        
        cpu_count = self.system_info['cpu_count']
        memory_gb = self.system_info['memory_gb']
        data_size_gb = self.data_info.get('total_size_gb', 1.0)
        
        # 内存建议
        if data_size_gb > memory_gb * 0.8:
            recommendations.append(
                f"⚠️ 数据大小({data_size_gb:.1f}GB)接近系统内存({memory_gb:.1f}GB)，建议启用磁盘溢出"
            )
            
        if memory_gb < 8:
            recommendations.append("💡 系统内存较小，建议减少Worker数量或增加内存")
            
        # CPU建议
        if cpu_count >= 8:
            recommendations.append("✅ CPU核心充足，可以启用更多Worker")
        elif cpu_count <= 2:
            recommendations.append("⚠️ CPU核心较少，建议使用单Worker多线程模式")
            
        # 数据规模建议
        if data_size_gb < 0.1:
            recommendations.append("💡 数据量较小，考虑使用单机版本可能更高效")
        elif data_size_gb > 50:
            recommendations.append("🚀 大数据集，建议使用集群模式以获得最佳性能")
            
        # 磁盘建议
        if PSUTIL_AVAILABLE and 'disk_free_gb' in self.system_info:
            free_space = self.system_info['disk_free_gb']
            if free_space < data_size_gb * 2:
                recommendations.append(
                    f"⚠️ 磁盘空间不足，剩余{free_space:.1f}GB，建议至少保留{data_size_gb*2:.1f}GB"
                )
                
        return recommendations
        
    def print_system_analysis(self):
        """打印系统分析报告"""
        print(f"\n{'='*60}")
        print("🖥️ 系统资源分析")
        print(f"{'='*60}")
        
        print(f"💻 CPU信息:")
        print(f"   - 逻辑核心: {self.system_info['cpu_count']}")
        if 'cpu_count_physical' in self.system_info:
            print(f"   - 物理核心: {self.system_info['cpu_count_physical']}")
        if 'cpu_freq' in self.system_info and self.system_info['cpu_freq']:
            freq = self.system_info['cpu_freq']
            if 'current' in freq:
                print(f"   - 当前频率: {freq['current']:.0f}MHz")
                
        print(f"\n💾 内存信息:")
        print(f"   - 总内存: {self.system_info['memory_gb']:.1f}GB")
        if 'memory_available_gb' in self.system_info:
            print(f"   - 可用内存: {self.system_info['memory_available_gb']:.1f}GB")
            print(f"   - 内存使用率: {self.system_info['memory_percent']:.1f}%")
            
        if 'disk_space_gb' in self.system_info:
            print(f"\n💿 磁盘信息:")
            print(f"   - 总空间: {self.system_info['disk_space_gb']:.1f}GB")
            if 'disk_free_gb' in self.system_info:
                print(f"   - 可用空间: {self.system_info['disk_free_gb']:.1f}GB")
                print(f"   - 磁盘使用率: {self.system_info['disk_percent']:.1f}%")
                
    def print_config_recommendations(self, config: Dict):
        """打印配置建议"""
        print(f"\n{'='*60}")
        print("🔧 Dask配置建议")
        print(f"{'='*60}")
        
        cluster = config['cluster']
        performance = config['performance']
        memory = config['memory']
        
        print(f"🏗️ 集群配置:")
        print(f"   - Worker数量: {cluster['n_workers']}")
        print(f"   - 每Worker线程数: {cluster['threads_per_worker']}")
        print(f"   - 每Worker内存限制: {cluster['memory_limit']}")
        
        print(f"\n⚡ 性能配置:")
        print(f"   - 数据块大小: {performance['chunk_size']}")
        print(f"   - 图优化: {'启用' if performance['optimize_graph'] else '禁用'}")
        print(f"   - 中间结果缓存: {'启用' if performance['cache_intermediate'] else '禁用'}")
        
        print(f"\n💾 内存管理:")
        print(f"   - 目标内存使用率: {memory['target_fraction']*100:.0f}%")
        print(f"   - 溢出阈值: {memory['spill_fraction']*100:.0f}%")
        print(f"   - 暂停阈值: {memory['pause_fraction']*100:.0f}%")
        
        if config['recommendations']:
            print(f"\n💡 优化建议:")
            for rec in config['recommendations']:
                print(f"   {rec}")
                
    def generate_command_line(self, config: Dict) -> str:
        """生成命令行参数"""
        cluster = config['cluster']
        performance = config['performance']
        
        cmd_parts = [
            "python sampler_dask.py",
            "--input '*.jsonl'",
            "--output sampled_data",
            "--ratio 0.1",
            f"--n-workers {cluster['n_workers']}",
            f"--threads-per-worker {cluster['threads_per_worker']}",
            f"--memory-limit {cluster['memory_limit']}",
            f"--chunk-size {performance['chunk_size']}"
        ]
        
        return " \\\n    ".join(cmd_parts)
        
    def save_config(self, config: Dict, output_file: str):
        """保存配置到文件"""
        full_config = {
            'system_info': self.system_info,
            'data_info': self.data_info,
            'dask_config': config,
            'command_line': self.generate_command_line(config)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(full_config, f, ensure_ascii=False, indent=2)
            
        print(f"📄 配置已保存到: {output_file}")


def main():
    parser = argparse.ArgumentParser(description='🔧 Dask配置优化器')
    parser.add_argument('--data-pattern', default='*.jsonl',
                        help='数据文件匹配模式 (默认: *.jsonl)')
    parser.add_argument('--output', default='dask_config.json',
                        help='配置输出文件 (默认: dask_config.json)')
    parser.add_argument('--analyze-only', action='store_true',
                        help='仅分析系统，不生成配置')
    parser.add_argument('--verbose', '-v', action='store_true',
                        help='详细输出')
    
    args = parser.parse_args()
    
    print("🔧 Dask配置优化器")
    print("=" * 50)
    
    optimizer = DaskConfigOptimizer()
    
    # 系统分析
    if args.verbose or args.analyze_only:
        optimizer.print_system_analysis()
        
    if args.analyze_only:
        return
        
    # 数据分析
    data_info = optimizer.analyze_data_pattern(args.data_pattern)
    
    # 生成配置
    config = optimizer.generate_optimal_config()
    
    # 打印建议
    optimizer.print_config_recommendations(config)
    
    # 生成命令行
    print(f"\n{'='*60}")
    print("🚀 推荐命令行")
    print(f"{'='*60}")
    print(optimizer.generate_command_line(config))
    
    # 保存配置
    optimizer.save_config(config, args.output)
    
    print(f"\n✅ 配置优化完成!")
    print(f"💡 使用建议:")
    print(f"   1. 查看配置文件: {args.output}")
    print(f"   2. 运行推荐命令")
    print(f"   3. 监控Dashboard: http://localhost:8787")


if __name__ == "__main__":
    main()
