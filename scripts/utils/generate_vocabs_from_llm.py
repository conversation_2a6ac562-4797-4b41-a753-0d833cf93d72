#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理文件，提取领域知识并输出为JSONL格式
支持多种大模型API (OpenAI, Claude, 本地模型等)
"""

import os
import json
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import time
import openai
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('domain_extraction.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DomainKnowledgeExtractor:
    def __init__(self, api_key: str, model_name: str = "gpt-3.5-turbo", base_url: Optional[str] = None):
        """
        初始化领域知识提取器

        Args:
            api_key: API密钥
            model_name: 模型名称
            base_url: API基础URL (可选，用于本地模型或其他API)
        """
        self.client = openai.OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        self.model_name = model_name

    def create_extraction_prompt(self, content: str) -> str:
        """创建领域知识提取的提示词"""
        prompt = f"""
请分析以下文档内容，识别其所属的领域，并提取核心的领域关键词。

要求：
1. 识别文档的主要领域（如：机器学习、数据科学、软件工程、医学、金融等）
2. 提取10-20个核心的领域关键词或术语
3. 按重要性排序关键词
4. 返回JSON格式，包含以下字段：
   - domain: 主要领域
   - sub_domains: 子领域列表（如有）
   - keywords: 关键词列表，每个关键词包含词汇和重要性评分(1-10)
   - summary: 文档核心内容概要（50字以内）

文档内容：
{content[:4000]}  # 限制长度避免token超限

请直接返回JSON格式的结果，不要包含其他说明文字。
"""
        return prompt

    def extract_domain_knowledge(self, content: str, filename: str) -> Dict[str, Any]:
        """
        从文档内容中提取领域知识

        Args:
            content: 文档内容
            filename: 文件名

        Returns:
            包含领域知识的字典
        """
        try:
            prompt = self.create_extraction_prompt(content)

            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system",
                     "content": "你是一个专业的领域知识分析专家，擅长识别文档的领域归属和提取关键术语。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )

            result_text = response.choices[0].message.content.strip()

            # 尝试解析JSON
            try:
                result = json.loads(result_text)
            except json.JSONDecodeError:
                # 如果不是标准JSON，尝试提取JSON部分
                import re
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                else:
                    raise ValueError("无法解析模型返回的JSON")

            # 添加元数据
            result.update({
                "filename": filename,
                "processed_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                "content_length": len(content),
                "model_used": self.model_name
            })

            return result

        except Exception as e:
            logger.error(f"处理文件 {filename} 时出错: {str(e)}")
            return {
                "filename": filename,
                "error": str(e),
                "processed_at": time.strftime("%Y-%m-%d %H:%M:%S")
            }

    def read_file_content(self, file_path: Path) -> str:
        """读取文件内容"""
        try:
            # 尝试不同编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        return f.read()
                except UnicodeDecodeError:
                    continue

            # 如果都失败，使用二进制读取并忽略错误
            with open(file_path, 'rb') as f:
                return f.read().decode('utf-8', errors='ignore')

        except Exception as e:
            logger.error(f"读取文件 {file_path} 失败: {str(e)}")
            return ""

    def process_file(self, file_path: Path) -> Dict[str, Any]:
        """处理单个文件"""
        content = self.read_file_content(file_path)
        if not content:
            return {
                "filename": file_path.name,
                "error": "文件内容为空或读取失败",
                "processed_at": time.strftime("%Y-%m-%d %H:%M:%S")
            }

        return self.extract_domain_knowledge(content, file_path.name)

    def process_directory(self, input_dir: str, output_file: str,
                          file_extensions: List[str] = ['.md', '.txt', '.py', '.js'],
                          max_workers: int = 3) -> None:
        """
        批量处理目录中的文件

        Args:
            input_dir: 输入目录路径
            output_file: 输出JSONL文件路径
            file_extensions: 要处理的文件扩展名列表
            max_workers: 并发处理的最大线程数
        """
        input_path = Path(input_dir)
        if not input_path.exists():
            logger.error(f"输入目录不存在: {input_dir}")
            return

        # 查找所有匹配的文件
        files_to_process = []
        for ext in file_extensions:
            files_to_process.extend(input_path.rglob(f"*{ext}"))

        if not files_to_process:
            logger.warning(f"在目录 {input_dir} 中未找到匹配的文件")
            return

        logger.info(f"找到 {len(files_to_process)} 个文件待处理")

        # 创建输出目录
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 并发处理文件
        with open(output_file, 'w', encoding='utf-8') as f:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_file = {
                    executor.submit(self.process_file, file_path): file_path
                    for file_path in files_to_process
                }

                # 处理完成的任务
                for future in tqdm(as_completed(future_to_file),
                                   total=len(files_to_process),
                                   desc="处理文件"):
                    file_path = future_to_file[future]
                    try:
                        result = future.result()
                        # 写入JSONL格式
                        f.write(json.dumps(result, ensure_ascii=False) + '\n')
                        f.flush()  # 确保及时写入

                        if 'error' not in result:
                            logger.info(f"成功处理: {file_path.name}")
                        else:
                            logger.warning(f"处理出错: {file_path.name} - {result['error']}")

                    except Exception as e:
                        logger.error(f"处理文件 {file_path} 时发生异常: {str(e)}")
                        # 写入错误记录
                        error_result = {
                            "filename": file_path.name,
                            "error": str(e),
                            "processed_at": time.strftime("%Y-%m-%d %H:%M:%S")
                        }
                        f.write(json.dumps(error_result, ensure_ascii=False) + '\n')

        logger.info(f"处理完成，结果保存到: {output_file}")


def main():
    parser = argparse.ArgumentParser(description='批量提取文件领域知识')
    parser.add_argument('input_dir', help='输入目录路径')
    parser.add_argument('output_file', help='输出JSONL文件路径')
    parser.add_argument('--api-key', required=True, help='API密钥')
    parser.add_argument('--model', default='gpt-3.5-turbo', help='模型名称')
    parser.add_argument('--base-url', help='API基础URL（可选）')
    parser.add_argument('--extensions', nargs='+',
                        default=['.md', '.txt', '.py', '.js', '.java', '.cpp', '.c', '.h'],
                        help='要处理的文件扩展名')
    parser.add_argument('--max-workers', type=int, default=3,
                        help='最大并发线程数')

    args = parser.parse_args()

    # 创建提取器
    extractor = DomainKnowledgeExtractor(
        api_key=args.api_key,
        model_name=args.model,
        base_url=args.base_url
    )

    # 开始批量处理
    extractor.process_directory(
        input_dir=args.input_dir,
        output_file=args.output_file,
        file_extensions=args.extensions,
        max_workers=args.max_workers
    )


if __name__ == "__main__":
    main()

# 使用示例：
# python domain_extractor.py /path/to/docs output.jsonl --api-key your_api_key
#
# 或者直接在代码中使用：
# extractor = DomainKnowledgeExtractor(api_key="your_api_key")
# extractor.process_directory("./docs", "domain_knowledge.jsonl")