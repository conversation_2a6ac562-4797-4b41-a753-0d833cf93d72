#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Alpaca格式到ShareGPT格式转换脚本
将Alpaca格式的JSONL文件转换为ShareGPT格式

Alpaca格式示例:
{
    "instruction": "解释什么是机器学习",
    "input": "",
    "output": "机器学习是人工智能的一个分支..."
}

ShareGPT格式示例:
{
    "conversations": [
        {"role": "user", "content": "解释什么是机器学习"},
        {"role": "assistant", "content": "机器学习是人工智能的一个分支..."}
    ]
}
"""

import json
import os
import argparse
from pathlib import Path
from typing import Dict, Any, List


def convert_alpaca_to_sharegpt(alpaca_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    将单条Alpaca格式数据转换为ShareGPT格式

    Args:
        alpaca_data: Alpaca格式的数据字典

    Returns:
        ShareGPT格式的数据字典
    """
    conversations = []

    # 构建用户输入
    if alpaca_data.get("input", "").strip():
        # 如果有input字段且不为空，将instruction和input合并
        user_message = f"{alpaca_data['instruction']}\n{alpaca_data['input']}"
    else:
        # 否则只使用instruction
        user_message = alpaca_data["instruction"]

    conversations.append({
        "role": "user",
        "content": user_message.strip()
    })

    # 添加助手回复
    if alpaca_data.get("output", "").strip():
        conversations.append({
            "role": "assistant",
            "content": alpaca_data["output"].strip()
        })

    return {"conversations": conversations}


def process_jsonl_file(input_file: str, output_file: str) -> None:
    """
    处理单个JSONL文件

    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
    """
    converted_count = 0
    error_count = 0

    print(f"正在处理: {input_file}")

    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
                open(output_file, 'w', encoding='utf-8') as outfile:

            for line_num, line in enumerate(infile, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    # 解析Alpaca格式数据
                    alpaca_data = json.loads(line)

                    # 验证必要字段
                    if "instruction" not in alpaca_data:
                        print(f"警告: 第{line_num}行缺少'instruction'字段，跳过")
                        error_count += 1
                        continue

                    if "output" not in alpaca_data:
                        print(f"警告: 第{line_num}行缺少'output'字段，跳过")
                        error_count += 1
                        continue

                    # 转换为ShareGPT格式
                    sharegpt_data = convert_alpaca_to_sharegpt(alpaca_data)

                    # 写入输出文件
                    json.dump(sharegpt_data, outfile, ensure_ascii=False, separators=(',', ':'))
                    outfile.write('\n')
                    converted_count += 1

                except json.JSONDecodeError as e:
                    print(f"错误: 第{line_num}行JSON解析失败: {e}")
                    error_count += 1
                    continue
                except Exception as e:
                    print(f"错误: 处理第{line_num}行时发生异常: {e}")
                    error_count += 1
                    continue

    except FileNotFoundError:
        print(f"错误: 输入文件不存在: {input_file}")
        return
    except Exception as e:
        print(f"错误: 处理文件时发生异常: {e}")
        return

    print(f"转换完成! 成功转换: {converted_count} 条，错误: {error_count} 条")
    print(f"输出文件: {output_file}")


def batch_convert(input_dir: str, output_file: str, pattern: str = "*.jsonl") -> None:
    """
    批量转换目录下的JSONL文件，合并到单个输出文件

    Args:
        input_dir: 输入目录路径
        output_file: 输出文件路径
        pattern: 文件匹配模式，默认为"*.jsonl"
    """
    input_path = Path(input_dir)

    if not input_path.exists():
        print(f"错误: 输入目录不存在: {input_dir}")
        return

    # 创建输出目录（如果需要）
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # 查找匹配的文件
    jsonl_files = list(input_path.glob(pattern))

    if not jsonl_files:
        print(f"警告: 在目录 {input_dir} 中未找到匹配 {pattern} 的文件")
        return

    print(f"找到 {len(jsonl_files)} 个文件待处理")
    print(f"将合并输出到: {output_file}")

    total_converted = 0
    total_errors = 0

    try:
        with open(output_file, 'w', encoding='utf-8') as outfile:
            for i, input_file in enumerate(jsonl_files, 1):
                print(f"[{i}/{len(jsonl_files)}] 正在处理: {input_file.name}")

                converted_count = 0
                error_count = 0

                try:
                    with open(input_file, 'r', encoding='utf-8') as infile:
                        for line_num, line in enumerate(infile, 1):
                            line = line.strip()
                            if not line:
                                continue

                            try:
                                # 解析Alpaca格式数据
                                alpaca_data = json.loads(line)

                                # 验证必要字段
                                if "instruction" not in alpaca_data:
                                    print(f"  警告: {input_file.name} 第{line_num}行缺少'instruction'字段，跳过")
                                    error_count += 1
                                    continue

                                if "output" not in alpaca_data:
                                    print(f"  警告: {input_file.name} 第{line_num}行缺少'output'字段，跳过")
                                    error_count += 1
                                    continue

                                # 转换为ShareGPT格式
                                sharegpt_data = convert_alpaca_to_sharegpt(alpaca_data)

                                # 写入输出文件
                                json.dump(sharegpt_data, outfile, ensure_ascii=False, separators=(',', ':'))
                                outfile.write('\n')
                                converted_count += 1

                            except json.JSONDecodeError as e:
                                print(f"  错误: {input_file.name} 第{line_num}行JSON解析失败: {e}")
                                error_count += 1
                                continue
                            except Exception as e:
                                print(f"  错误: 处理{input_file.name} 第{line_num}行时发生异常: {e}")
                                error_count += 1
                                continue

                except FileNotFoundError:
                    print(f"  错误: 文件不存在: {input_file}")
                    error_count += 1
                except Exception as e:
                    print(f"  错误: 处理文件 {input_file} 时发生异常: {e}")
                    error_count += 1

                print(f"  完成: 转换 {converted_count} 条，错误 {error_count} 条")
                total_converted += converted_count
                total_errors += error_count

    except Exception as e:
        print(f"错误: 创建输出文件时发生异常: {e}")
        return

    print("=" * 50)
    print(f"批量转换完成!")
    print(f"总计转换: {total_converted} 条")
    print(f"总计错误: {total_errors} 条")
    print(f"输出文件: {output_file}")


def main():
    parser = argparse.ArgumentParser(description="将Alpaca格式JSONL文件转换为ShareGPT格式")
    parser.add_argument("input", help="输入文件路径或目录路径")
    parser.add_argument("-o", "--output", help="输出文件路径")
    parser.add_argument("-p", "--pattern", default="*.jsonl", help="批量处理时的文件匹配模式 (默认: *.jsonl)")
    parser.add_argument("--batch", action="store_true", help="批量处理模式，将多个文件合并到一个输出文件")

    args = parser.parse_args()

    if args.batch:
        # 批量处理模式 - 合并到单个文件
        if args.output:
            output_file = args.output
        else:
            # 自动生成输出文件名
            input_name = Path(args.input).name
            output_file = f"{input_name}_sharegpt.jsonl"

        batch_convert(args.input, output_file, args.pattern)
    else:
        # 单文件处理模式
        input_file = args.input
        if args.output:
            output_file = args.output
        else:
            # 自动生成输出文件名
            input_path = Path(input_file)
            output_file = str(input_path.parent / f"{input_path.stem}_sharegpt.jsonl")

        process_jsonl_file(input_file, output_file)


if __name__ == "__main__":
    # 如果直接运行脚本，可以在这里设置默认参数进行测试
    import sys

    if len(sys.argv) == 1:
        print("使用方法:")
        print("单文件转换: python script.py input.jsonl [-o output.jsonl]")
        print("批量转换: python script.py input_dir --batch [-o output.jsonl] [-p '*.jsonl']")
        print()
        print("示例:")
        print("python script.py data.jsonl")
        print("python script.py data.jsonl -o sharegpt_data.jsonl")
        print("python script.py ./data --batch -o merged_sharegpt.jsonl")
        print("python script.py ./data --batch -p 'train_*.jsonl' -o train_sharegpt.jsonl")
        print()
        print("注意: 批量模式会将所有匹配的文件合并到一个输出文件中")
    else:
        main()