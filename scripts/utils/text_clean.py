import json
import argparse
import os


def clean_text(text):
    """
    清理文本，去除 <|im_start|> 和 <|im_end|> 标签

    Args:
        text (str): 原始文本

    Returns:
        str: 清理后的文本
    """
    # 去除开始和结束标签
    cleaned = text.replace("<|im_start|>", "").replace("<|im_end|>", "")
    # 去除多余的空白字符
    return cleaned.strip()


def process_jsonl_file(input_file, output_file, source="unicom-domain", lang="zh"):
    """
    处理JSONL文件

    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径
        source (str): source字段值
        lang (str): lang字段值
    """
    processed_count = 0
    error_count = 0

    print(f"正在处理文件: {input_file}")

    with open(input_file, 'r', encoding='utf-8') as infile, \
            open(output_file, 'w', encoding='utf-8') as outfile:

        for line_num, line in enumerate(infile, 1):
            try:
                # 解析JSON
                data = json.loads(line.strip())

                # 检查是否包含text字段
                if 'text' not in data:
                    print(f"警告: 第 {line_num} 行缺少 'text' 字段")
                    error_count += 1
                    continue

                # 清理text字段
                original_text = data['text']
                cleaned_text = clean_text(original_text)

                # 创建新的数据结构
                processed_data = {
                    'text': cleaned_text,
                    'source': source,
                    'lang': lang
                }

                # 写入处理后的数据
                outfile.write(json.dumps(processed_data, ensure_ascii=False) + '\n')
                processed_count += 1

                # 每处理1000行显示一次进度
                if line_num % 1000 == 0:
                    print(f"已处理 {line_num} 行...")

            except json.JSONDecodeError as e:
                print(f"错误: 第 {line_num} 行JSON解析失败: {e}")
                error_count += 1
                continue
            except Exception as e:
                print(f"错误: 第 {line_num} 行处理失败: {e}")
                error_count += 1
                continue

    print(f"处理完成!")
    print(f"成功处理: {processed_count} 行")
    print(f"错误行数: {error_count} 行")
    print(f"输出文件: {output_file}")


def main():
    parser = argparse.ArgumentParser(description='JSONL文件处理工具 - 清理文本并添加字段')
    parser.add_argument('input', help='输入JSONL文件路径')
    parser.add_argument('-o', '--output', help='输出JSONL文件路径 (默认: 输入文件名_processed.jsonl)')
    parser.add_argument('-s', '--source', default='unicom-domain',
                        help='source字段值 (默认: unicom-domain)')
    parser.add_argument('-l', '--lang', default='zh',
                        help='lang字段值 (默认: zh)')

    args = parser.parse_args()

    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        print(f"错误: 输入文件 '{args.input}' 不存在")
        return

    # 设置输出文件路径
    if args.output:
        output_file = args.output
    else:
        # 自动生成输出文件名
        base_name = os.path.splitext(args.input)[0]
        output_file = f"{base_name}_processed.jsonl"

    # 检查输出文件是否已存在
    if os.path.exists(output_file):
        response = input(f"输出文件 '{output_file}' 已存在，是否覆盖？ (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("操作已取消")
            return

    try:
        process_jsonl_file(args.input, output_file, args.source, args.lang)
    except Exception as e:
        print(f"处理失败: {e}")


if __name__ == "__main__":
    main()

# 使用示例:
# 1. 基本用法
#    python processor.py input.jsonl
#
# 2. 指定输出文件
#    python processor.py input.jsonl -o output.jsonl
#
# 3. 自定义source和lang字段
#    python processor.py input.jsonl -s "my-source" -l "en"

# 处理前示例:
# {"text": "<|im_start|># 中华人民共和国国家标准 <|im_end|>"}
#
# 处理后示例:
# {"text": "# 中华人民共和国国家标准", "source": "unicom-domain", "lang": "zh"}
