#!/usr/bin/env python3
"""
🚀 Dask抽样工具完整安装脚本
自动安装Dask和相关依赖，配置环境

功能：
1. 检查Python环境
2. 安装Dask完整版
3. 验证安装
4. 配置优化
5. 使用指南
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


class DaskEnvironmentSetup:
    """Dask环境安装器"""
    
    def __init__(self):
        self.python_version = sys.version_info
        self.system = platform.system().lower()
        
    def check_python_version(self) -> bool:
        """检查Python版本"""
        print("🐍 检查Python环境...")
        
        if self.python_version < (3, 8):
            print(f"❌ Python版本过低: {sys.version}")
            print("Dask需要Python 3.8或更高版本")
            return False
            
        print(f"✅ Python版本: {sys.version}")
        return True
        
    def install_dask(self) -> bool:
        """安装Dask完整版"""
        print("📦 安装Dask...")
        
        # 检查是否已安装
        try:
            import dask
            print(f"✅ Dask已安装: {dask.__version__}")
            
            # 检查distributed
            try:
                import distributed
                print(f"✅ Distributed已安装: {distributed.__version__}")
            except ImportError:
                print("⚠️ Distributed未安装，正在安装...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'distributed'], check=True)
                
            return True
            
        except ImportError:
            print("📦 Dask未安装，开始安装...")
            
        try:
            # 安装Dask完整版
            packages = [
                'dask[complete]',  # 完整版Dask
                'distributed',     # 分布式计算
                'bokeh>=2.4.2',   # Dashboard依赖
                'psutil',         # 系统监控
                'cloudpickle',    # 序列化
                'toolz',          # 函数式编程工具
                'partd',          # 分区数据结构
            ]
            
            for package in packages:
                print(f"   安装 {package}...")
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install', package
                ], check=True, capture_output=True)
                
            print("✅ Dask安装成功")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Dask安装失败: {e}")
            return False
            
    def verify_installation(self) -> bool:
        """验证安装"""
        print("🔍 验证Dask安装...")
        
        try:
            # 测试基本导入
            import dask
            import dask.bag as db
            import dask.dataframe as dd
            from dask.distributed import Client
            import distributed
            
            print(f"✅ 基本导入成功")
            print(f"   - Dask: {dask.__version__}")
            print(f"   - Distributed: {distributed.__version__}")
            
            # 测试本地集群
            print("🧪 测试本地集群...")
            
            with Client(processes=False, threads_per_worker=2, n_workers=2) as client:
                print(f"✅ 本地集群创建成功")
                print(f"   - Workers: {len(client.scheduler_info()['workers'])}")
                print(f"   - Dashboard: {client.dashboard_link}")
                
                # 简单计算测试
                data = list(range(1000))
                bag = db.from_sequence(data, partition_size=100)
                result = bag.map(lambda x: x * 2).sum().compute()
                
                expected = sum(x * 2 for x in data)
                if result == expected:
                    print("✅ 计算测试通过")
                else:
                    print(f"❌ 计算测试失败: {result} != {expected}")
                    return False
                    
            return True
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False
            
    def create_config_files(self):
        """创建配置文件"""
        print("📄 创建配置文件...")
        
        # Dask配置文件
        dask_config = {
            "distributed": {
                "worker": {
                    "memory": {
                        "target": 0.6,
                        "spill": 0.7,
                        "pause": 0.8,
                        "terminate": 0.95
                    }
                },
                "comm": {
                    "retry": {
                        "delay": "1s"
                    },
                    "timeouts": {
                        "connect": "30s",
                        "tcp": "30s"
                    }
                }
            },
            "dataframe": {
                "query-planning": False
            },
            "array": {
                "chunk-size": "128MiB"
            }
        }
        
        # 保存YAML配置
        try:
            import yaml
            config_file = Path("dask.yaml")
            with open(config_file, 'w') as f:
                yaml.dump(dask_config, f, default_flow_style=False)
            print(f"✅ Dask配置已保存到: {config_file}")
        except ImportError:
            print("⚠️ PyYAML未安装，跳过YAML配置文件创建")
            
        # 创建环境脚本
        env_script = Path("dask_env.sh")
        with open(env_script, 'w') as f:
            f.write("#!/bin/bash\n")
            f.write("# Dask环境配置\n\n")
            f.write("export DASK_CONFIG=./dask.yaml\n")
            f.write("export DASK_DISTRIBUTED__WORKER__DAEMON=False\n")
            f.write("export DASK_DISTRIBUTED__ADMIN__TICK__LIMIT=3s\n")
            
        env_script.chmod(0o755)
        print(f"✅ 环境脚本已保存到: {env_script}")
        
    def print_usage_guide(self):
        """打印使用指南"""
        print("\n" + "=" * 60)
        print("🚀 Dask抽样工具使用指南")
        print("=" * 60)
        
        print("\n📋 基本使用:")
        print("# 本地模式 (推荐)")
        print("python sampler_dask.py --input '*.jsonl' --output sampled_data --ratio 0.1")
        
        print("\n# 指定资源配置")
        print("python sampler_dask.py \\")
        print("    --input 'data/*.jsonl' \\")
        print("    --output sampled_data \\")
        print("    --ratio 0.1 \\")
        print("    --n-workers 4 \\")
        print("    --threads-per-worker 2 \\")
        print("    --memory-limit 4GB")
        
        print("\n🔧 配置优化:")
        print("# 生成优化配置")
        print("python dask_config_optimizer.py --data-pattern '*.jsonl'")
        
        print("\n🏗️ 集群管理:")
        print("# 启动独立集群")
        print("python dask_deployment.py start")
        print("")
        print("# 连接到集群")
        print("python sampler_dask.py --scheduler tcp://localhost:8786")
        print("")
        print("# 停止集群")
        print("python dask_deployment.py stop")
        
        print("\n📊 监控和调试:")
        print("# Dashboard访问")
        print("http://localhost:8787")
        print("")
        print("# 集群状态检查")
        print("python dask_deployment.py health")
        
        print("\n💡 性能建议:")
        print("- 小数据集(<1GB): 使用 --n-workers 2")
        print("- 中等数据集(1-10GB): 使用 --n-workers 4")
        print("- 大数据集(>10GB): 考虑独立集群模式")
        print("- 内存充足时增加 --memory-limit")
        print("- CPU核心多时增加 --n-workers")
        
        print("\n🆚 版本对比:")
        print("# 四版本性能对比")
        print("python benchmark_all_samplers.py")
        
        print("\n🔗 相关文件:")
        print("- sampler_dask.py          # Dask分布式抽样脚本")
        print("- dask_config_optimizer.py # 配置优化器")
        print("- dask_deployment.py       # 集群部署管理")
        print("- benchmark_all_samplers.py # 性能对比测试")


def main():
    print("🚀 Dask抽样工具完整安装")
    print("=" * 50)
    
    setup = DaskEnvironmentSetup()
    
    # 检查Python版本
    if not setup.check_python_version():
        print("❌ Python版本不兼容，请升级到Python 3.8+")
        return
        
    # 安装Dask
    if not setup.install_dask():
        print("❌ Dask安装失败")
        return
        
    # 验证安装
    if not setup.verify_installation():
        print("❌ Dask验证失败")
        return
        
    # 创建配置文件
    setup.create_config_files()
    
    print("\n🎉 Dask环境安装成功!")
    
    # 打印使用指南
    setup.print_usage_guide()
    
    print("\n✅ 安装完成!")
    print("💡 下一步:")
    print("1. 运行: python dask_config_optimizer.py --data-pattern '*.jsonl'")
    print("2. 测试: python sampler_dask.py --input 'test_data.jsonl' --output test_output --ratio 0.1")
    print("3. 监控: 访问 http://localhost:8787")


if __name__ == "__main__":
    main()
