#!/usr/bin/env python3
"""
极速优化版JSONL文件抽样工具
性能优化重点：
1. 使用orjson进行超快JSON解析
2. 优化的文件I/O和内存管理
3. 智能并发处理和负载均衡
4. 详细的性能监控和分析
"""

import os
import sys
import glob
import random
import math
import time
import argparse
import threading
from collections import defaultdict, deque
from typing import Dict, List, Tuple, Set, Optional, Iterator, Union
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from functools import partial
import mmap
import psutil
import gc

# 尝试导入高性能JSON库
try:
    import orjson
    JSON_LOADS = orjson.loads
    JSON_DUMPS = lambda obj: orjson.dumps(obj, option=orjson.OPT_INDENT_2).decode()
    JSON_DUMPS_COMPACT = lambda obj: orjson.dumps(obj).decode()
    print("✓ 使用orjson高性能JSON库")
except ImportError:
    import json
    JSON_LOADS = json.loads
    JSON_DUMPS = lambda obj: json.dumps(obj, ensure_ascii=False, indent=2)
    JSON_DUMPS_COMPACT = lambda obj: json.dumps(obj, ensure_ascii=False, separators=(',', ':'))
    print("⚠ 使用标准json库，建议安装orjson: pip install orjson")


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.checkpoints = {}
        self.memory_usage = []
        self.process = psutil.Process()
        
    def checkpoint(self, name: str):
        """记录检查点"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        memory_mb = self.process.memory_info().rss / 1024 / 1024
        
        self.checkpoints[name] = {
            'time': current_time,
            'elapsed': elapsed,
            'memory_mb': memory_mb
        }
        self.memory_usage.append((elapsed, memory_mb))
        
    def get_stats(self) -> Dict:
        """获取性能统计"""
        total_time = time.time() - self.start_time
        peak_memory = max(usage[1] for usage in self.memory_usage) if self.memory_usage else 0
        
        return {
            'total_time': total_time,
            'peak_memory_mb': peak_memory,
            'checkpoints': self.checkpoints,
            'cpu_count': os.cpu_count(),
            'memory_usage_timeline': self.memory_usage
        }
        
    def print_summary(self):
        """打印性能摘要"""
        stats = self.get_stats()
        print(f"\n{'='*60}")
        print("🚀 性能分析报告")
        print(f"{'='*60}")
        print(f"⏱️  总耗时: {stats['total_time']:.2f}秒")
        print(f"💾 峰值内存: {stats['peak_memory_mb']:.1f}MB")
        print(f"🖥️  CPU核心数: {stats['cpu_count']}")
        
        if len(self.checkpoints) > 1:
            print(f"\n📊 各阶段耗时:")
            prev_time = self.start_time
            for name, data in self.checkpoints.items():
                stage_time = data['time'] - prev_time
                percentage = (stage_time / stats['total_time']) * 100
                print(f"  - {name}: {stage_time:.2f}s ({percentage:.1f}%)")
                prev_time = data['time']


class OptimizedProgressTracker:
    """优化的进度跟踪器"""
    
    def __init__(self, total: int, description: str = "Processing"):
        self.total = total
        self.current = 0
        self.description = description
        self.start_time = time.time()
        self.lock = threading.Lock()
        self.last_update = 0
        self.update_interval = 2.0  # 2秒更新一次
        self.rate_samples = deque(maxlen=10)  # 保持最近10个速率样本
        
    def update(self, amount: int = 1):
        with self.lock:
            self.current += amount
            current_time = time.time()
            
            # 计算瞬时速率
            if self.last_update > 0:
                time_diff = current_time - self.last_update
                if time_diff > 0:
                    rate = amount / time_diff
                    self.rate_samples.append(rate)
            
            # 减少显示频率
            if current_time - self.last_update > self.update_interval or self.current >= self.total:
                self._display_progress()
                self.last_update = current_time
                
    def _display_progress(self):
        elapsed = time.time() - self.start_time
        if self.current > 0 and elapsed > 0:
            # 使用平滑的速率计算
            if self.rate_samples:
                avg_rate = sum(self.rate_samples) / len(self.rate_samples)
            else:
                avg_rate = self.current / elapsed
                
            eta = (self.total - self.current) / avg_rate if avg_rate > 0 else 0
            percentage = (self.current / self.total) * 100
            
            # 简化的进度条
            bar_length = 30
            filled_length = int(bar_length * self.current / self.total)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            
            print(f'\r{self.description}: {bar} {percentage:.1f}% '
                  f'({self.current:,}/{self.total:,}) '
                  f'{avg_rate:.0f}/s ETA: {eta:.0f}s', end='', flush=True)
            
            if self.current >= self.total:
                print(f'\n✓ {self.description} 完成 ({elapsed:.1f}s)')


def read_file_ultra_fast(file_path: str, encoding: str = 'utf-8') -> Iterator[Tuple[int, str]]:
    """超快文件读取器"""
    try:
        file_size = os.path.getsize(file_path)
        
        # 小文件直接读取
        if file_size < 5 * 1024 * 1024:  # 5MB以下
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                for line_num, line in enumerate(f):
                    line = line.strip()
                    if line:
                        yield line_num, line
            return
            
        # 大文件使用优化的内存映射
        with open(file_path, 'rb') as f:
            with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mmapped_file:
                line_num = 0
                start = 0
                
                # 使用更大的块大小
                chunk_size = 64 * 1024  # 64KB
                
                while start < file_size:
                    # 读取一个块
                    end = min(start + chunk_size, file_size)
                    chunk = mmapped_file[start:end]
                    
                    # 找到最后一个完整行的位置
                    if end < file_size:
                        last_newline = chunk.rfind(b'\n')
                        if last_newline != -1:
                            chunk = chunk[:last_newline + 1]
                            end = start + last_newline + 1
                    
                    # 解码并处理行
                    try:
                        text = chunk.decode(encoding, errors='ignore')
                        for line in text.splitlines():
                            line = line.strip()
                            if line:
                                yield line_num, line
                            line_num += 1
                    except:
                        pass
                    
                    start = end
                    
    except Exception:
        # 回退到标准读取
        with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
            for line_num, line in enumerate(f):
                line = line.strip()
                if line:
                    yield line_num, line


def process_file_batch(file_path: str, batch_size: int = 50000) -> Tuple[List[dict], Dict[str, List[int]], int]:
    """批量处理单个文件"""
    file_data = []
    file_source_indices = defaultdict(list)
    error_count = 0
    
    # 预分配列表以减少内存重分配
    batch_data = []
    
    try:
        for line_num, line in read_file_ultra_fast(file_path):
            try:
                data = JSON_LOADS(line)
                if 'source' in data:
                    batch_data.append(data)
                    
                    # 批量处理
                    if len(batch_data) >= batch_size:
                        base_index = len(file_data)
                        # 使用列表推导式优化
                        for i, item in enumerate(batch_data):
                            file_source_indices[item['source']].append(base_index + i)
                        file_data.extend(batch_data)
                        batch_data.clear()
                else:
                    error_count += 1
                    
            except:
                error_count += 1
                
        # 处理剩余数据
        if batch_data:
            base_index = len(file_data)
            for i, item in enumerate(batch_data):
                file_source_indices[item['source']].append(base_index + i)
            file_data.extend(batch_data)
            
    except Exception as e:
        print(f"\n错误: 处理文件 {os.path.basename(file_path)} 失败: {e}")
        return [], {}, 0
        
    return file_data, file_source_indices, error_count


def estimate_file_lines_smart(file_path: str) -> int:
    """智能估算文件行数"""
    try:
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            return 0
            
        # 对于小文件，采样更多
        if file_size < 10 * 1024 * 1024:  # 10MB以下
            sample_size = min(1024 * 1024, file_size // 2)  # 采样1MB或文件的一半
        else:
            sample_size = 1024 * 1024  # 大文件采样1MB
            
        with open(file_path, 'rb') as f:
            sample = f.read(sample_size)
            line_count = sample.count(b'\n')
            
        # 估算总行数
        if sample_size > 0:
            estimated_lines = int((line_count * file_size) / sample_size)
            return max(estimated_lines, 100)  # 至少100行
            
    except:
        pass
        
    return 5000  # 默认估算


def read_jsonl_files_ultra_parallel(file_pattern: str, max_workers: int = None) -> Tuple[List[dict], Dict[str, List[int]]]:
    """超级并行读取JSONL文件"""
    monitor = PerformanceMonitor()
    monitor.checkpoint("开始处理")
    
    # 获取文件列表
    files = glob.glob(file_pattern)
    if not files:
        raise ValueError(f"没有找到匹配模式 '{file_pattern}' 的文件")
        
    # 按文件大小排序，大文件优先（更好的负载均衡）
    files.sort(key=lambda f: os.path.getsize(f) if os.path.exists(f) else 0, reverse=True)
    
    print(f"找到 {len(files)} 个JSONL文件")
    monitor.checkpoint("文件发现完成")
    
    # 快速估算总行数
    print("智能估算数据量...")
    total_estimated = sum(estimate_file_lines_smart(f) for f in files[:min(5, len(files))])
    if len(files) > 5:
        avg_lines = total_estimated // min(5, len(files))
        total_estimated = avg_lines * len(files)
    
    print(f"预估总行数: {total_estimated:,}")
    monitor.checkpoint("行数估算完成")
    
    # 优化并发数
    cpu_count = os.cpu_count() or 1
    if max_workers is None:
        # 根据文件数量和系统资源动态调整
        optimal_workers = min(len(files), cpu_count * 3, 20)  # 最多20个线程
    else:
        optimal_workers = min(max_workers, len(files))
        
    print(f"使用 {optimal_workers} 个线程并行处理")
    
    # 进度跟踪
    progress = OptimizedProgressTracker(len(files), "读取文件")
    
    all_data = []
    source_indices = defaultdict(list)
    total_errors = 0
    data_lock = threading.Lock()
    
    def process_file_wrapper(file_path: str):
        """文件处理包装器"""
        result = process_file_batch(file_path)
        progress.update(1)
        return file_path, result[0], result[1], result[2]
    
    monitor.checkpoint("开始并行读取")
    
    # 使用线程池处理
    with ThreadPoolExecutor(max_workers=optimal_workers, thread_name_prefix="FileReader") as executor:
        # 提交所有任务
        futures = {executor.submit(process_file_wrapper, file_path): file_path for file_path in files}
        
        # 收集结果
        for future in as_completed(futures):
            try:
                file_path, file_data, file_source_indices, error_count = future.result()
                
                if file_data:
                    with data_lock:
                        base_index = len(all_data)
                        # 批量更新索引
                        for source, indices in file_source_indices.items():
                            adjusted_indices = [idx + base_index for idx in indices]
                            source_indices[source].extend(adjusted_indices)
                        
                        all_data.extend(file_data)
                        total_errors += error_count
                        
            except Exception as e:
                file_path = futures[future]
                print(f"\n错误: 处理文件 {file_path} 时发生异常: {e}")
    
    monitor.checkpoint("并行读取完成")
    
    print(f"\n总共读取 {len(all_data):,} 条有效数据")
    if total_errors > 0:
        print(f"跳过 {total_errors} 条错误记录")
        
    print(f"发现 {len(source_indices)} 个不同的source:")
    for source, indices in sorted(source_indices.items()):
        print(f"  - {source}: {len(indices):,} 条数据")
    
    monitor.checkpoint("数据统计完成")
    monitor.print_summary()

    return all_data, source_indices


def load_sampling_records_ultra_fast(record_files: List[str]) -> Dict[str, Set[int]]:
    """超快记录加载"""
    sampled_indices = defaultdict(set)

    if not record_files:
        return sampled_indices

    print("加载历史采样记录...")

    for record_file in record_files:
        if not os.path.exists(record_file):
            continue

        try:
            with open(record_file, 'r', encoding='utf-8') as f:
                record_data = JSON_LOADS(f.read())

            if 'source_details' in record_data:
                for source, indices in record_data['source_details'].items():
                    sampled_indices[source].update(indices)

        except Exception as e:
            print(f"警告: 记录文件 {record_file} 加载失败: {e}")

    if sampled_indices:
        total_previous = sum(len(indices) for indices in sampled_indices.values())
        print(f"加载了 {total_previous:,} 条历史采样记录")

    return sampled_indices


def sample_data_ultra_fast(all_data: List[dict], remaining_indices: Dict[str, List[int]],
                          sample_ratio: float, random_seed: int = 42) -> Tuple[List[dict], Dict[str, List[int]]]:
    """超快采样函数"""
    if not 0 < sample_ratio <= 1.0:
        raise ValueError("抽样比例必须在 0 和 1 之间")

    random.seed(random_seed)

    # 预计算采样计划
    sampling_plan = {}
    total_to_sample = 0

    print(f"\n计算采样数量 (比例: {sample_ratio * 100:.1f}%):")

    for source, indices in remaining_indices.items():
        if indices:
            sample_count = max(1, int(len(indices) * sample_ratio))
            sample_count = min(sample_count, len(indices))
            sampling_plan[source] = sample_count
            total_to_sample += sample_count
            print(f"  - {source}: {len(indices):,} -> {sample_count:,}")
        else:
            sampling_plan[source] = 0

    if total_to_sample == 0:
        print("没有数据可以采样")
        return [], {}

    # 预分配结果数组
    sampled_data = [None] * total_to_sample
    sampled_indices = {}
    current_idx = 0

    print(f"\n开始采样，总计 {total_to_sample:,} 条数据...")
    progress = OptimizedProgressTracker(len(sampling_plan), "采样进度")

    for source, indices in remaining_indices.items():
        sample_count = sampling_plan[source]
        if sample_count == 0:
            sampled_indices[source] = []
            progress.update(1)
            continue

        # 使用numpy风格的随机采样（如果可用）
        try:
            import numpy as np
            sampled_source_indices = np.random.choice(indices, sample_count, replace=False).tolist()
        except ImportError:
            sampled_source_indices = random.sample(indices, sample_count)

        sampled_source_indices.sort()

        # 批量填充
        for i, idx in enumerate(sampled_source_indices):
            sampled_data[current_idx + i] = all_data[idx]

        sampled_indices[source] = sampled_source_indices
        current_idx += sample_count
        progress.update(1)

    print(f"\n采样完成，共 {total_to_sample:,} 条数据")
    return sampled_data, sampled_indices


def write_files_ultra_fast(sampled_data: List[dict], output_prefix: str,
                          max_lines_per_file: int = 10000) -> List[str]:
    """超快文件写入"""
    if not sampled_data:
        return []

    num_files = math.ceil(len(sampled_data) / max_lines_per_file)
    output_files = []

    print(f"\n分割为 {num_files} 个文件...")
    progress = OptimizedProgressTracker(num_files, "写入文件")

    # 使用更大的缓冲区
    buffer_size = 2 * 1024 * 1024  # 2MB缓冲区

    for file_idx in range(num_files):
        start_idx = file_idx * max_lines_per_file
        end_idx = min(start_idx + max_lines_per_file, len(sampled_data))

        if num_files == 1:
            output_file = f"{output_prefix}.jsonl"
        else:
            output_file = f"{output_prefix}_part{file_idx + 1:03d}.jsonl"

        # 批量构建JSON字符串
        json_lines = []
        batch_size = 10000

        for batch_start in range(start_idx, end_idx, batch_size):
            batch_end = min(batch_start + batch_size, end_idx)
            batch_lines = [JSON_DUMPS_COMPACT(sampled_data[i]) for i in range(batch_start, batch_end)]
            json_lines.extend(batch_lines)

        # 一次性写入所有数据
        with open(output_file, 'w', encoding='utf-8', buffering=buffer_size) as f:
            f.write('\n'.join(json_lines) + '\n')

        output_files.append(output_file)
        progress.update(1)

    return output_files


def main():
    parser = argparse.ArgumentParser(description='🚀 超级优化JSONL文件采样工具')
    parser.add_argument('--input', '-i', default='*.jsonl',
                        help='输入文件匹配模式 (默认: *.jsonl)')
    parser.add_argument('--output', '-o', default='sampled_data',
                        help='输出文件前缀 (默认: sampled_data)')
    parser.add_argument('--ratio', '-r', type=float, default=0.1,
                        help='抽样比例 (默认: 0.1 即10%%)')
    parser.add_argument('--seed', '-s', type=int, default=42,
                        help='随机种子 (默认: 42)')
    parser.add_argument('--record', default='sampling_record.json',
                        help='本次索引记录文件名 (默认: sampling_record.json)')
    parser.add_argument('--previous-records', nargs='*', default=[],
                        help='之前的采样记录文件路径列表')
    parser.add_argument('--max-lines', type=int, default=10000,
                        help='每个输出文件的最大行数 (默认: 10,000)')
    parser.add_argument('--max-workers', type=int, default=None,
                        help='并行读取的最大线程数 (默认: 自动)')
    parser.add_argument('--profile', action='store_true',
                        help='启用详细性能分析')

    args = parser.parse_args()

    if not 0 < args.ratio <= 1.0:
        print("错误: 抽样比例必须在 0 和 1 之间")
        return

    # 全局性能监控
    global_monitor = PerformanceMonitor()
    global_monitor.checkpoint("程序开始")

    try:
        # 读取数据
        print("=" * 60)
        print("🚀 第一步: 超级并行读取")
        print("=" * 60)
        all_data, source_indices = read_jsonl_files_ultra_parallel(args.input, args.max_workers)
        global_monitor.checkpoint("数据读取完成")

        if not all_data:
            print("错误: 没有找到有效的数据")
            return

        # 强制垃圾回收
        gc.collect()

        # 加载记录
        print("\n" + "=" * 60)
        print("📚 第二步: 加载历史记录")
        print("=" * 60)
        previous_sampled = load_sampling_records_ultra_fast(args.previous_records)
        global_monitor.checkpoint("记录加载完成")

        # 计算剩余数据
        print("\n" + "=" * 60)
        print("🧮 第三步: 计算剩余数据")
        print("=" * 60)

        remaining_indices = {}
        for source, all_indices in source_indices.items():
            sampled_set = previous_sampled.get(source, set())
            if not sampled_set:
                remaining = all_indices
            else:
                # 使用集合操作优化
                all_set = set(all_indices)
                remaining = list(all_set - sampled_set)
            remaining_indices[source] = remaining
            print(f"  - {source}: {len(all_indices):,} 总数据, "
                  f"{len(sampled_set):,} 已采样, {len(remaining):,} 剩余")

        global_monitor.checkpoint("剩余数据计算完成")

        total_remaining = sum(len(indices) for indices in remaining_indices.values())
        if total_remaining == 0:
            print("警告: 没有剩余数据可以采样")
            return

        # 采样
        print("\n" + "=" * 60)
        print("🎯 第四步: 超快采样")
        print("=" * 60)
        sampled_data, sampled_indices = sample_data_ultra_fast(
            all_data, remaining_indices, args.ratio, args.seed
        )
        global_monitor.checkpoint("采样完成")

        if not sampled_data:
            print("没有采样到任何数据")
            return

        # 保存文件
        print("\n" + "=" * 60)
        print("💾 第五步: 超快写入")
        print("=" * 60)
        output_files = write_files_ultra_fast(sampled_data, args.output, args.max_lines)
        global_monitor.checkpoint("文件写入完成")

        # 保存记录
        record_data = {
            'timestamp': datetime.now().isoformat(),
            'sample_ratio': args.ratio,
            'random_seed': args.seed,
            'total_sampled': len(sampled_data),
            'output_files': output_files,
            'source_details': sampled_indices
        }

        with open(args.record, 'w', encoding='utf-8') as f:
            f.write(JSON_DUMPS(record_data))

        global_monitor.checkpoint("记录保存完成")

        # 最终统计
        stats = global_monitor.get_stats()
        total_time = stats['total_time']

        print("\n" + "=" * 60)
        print("🎉 超级采样完成!")
        print("=" * 60)
        print(f"⏱️  总耗时: {total_time:.2f}秒")
        print(f"🔥 处理速度: {len(all_data) / total_time:.0f} 条/秒")
        print(f"💾 峰值内存: {stats['peak_memory_mb']:.1f}MB")
        print(f"📊 原始数据: {len(all_data):,} 条")
        print(f"📈 本次采样: {len(sampled_data):,} 条")
        print(f"📁 输出文件: {len(output_files)} 个")

        if args.profile:
            global_monitor.print_summary()

        if total_time > 60:
            minutes = int(total_time // 60)
            seconds = total_time % 60
            print(f"⏱️  详细耗时: {minutes}分{seconds:.1f}秒")

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
