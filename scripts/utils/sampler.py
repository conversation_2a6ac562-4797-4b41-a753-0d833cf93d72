import json
import os
import glob
import random
import math
import time
from collections import defaultdict
from typing import Dict, List, Tuple, Set, Optional, Iterator
import argparse
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed, ProcessPoolExecutor
import threading
from functools import partial
import mmap
import io


class ProgressTracker:
    """优化的进度跟踪器，减少锁竞争和输出频率"""

    def __init__(self, total: int, description: str = "Processing"):
        self.total = total
        self.current = 0
        self.description = description
        self.start_time = time.time()
        self.lock = threading.Lock()
        self.last_update = 0
        self.update_interval = 1.0  # 增加更新间隔到1秒

    def update(self, amount: int = 1):
        with self.lock:
            self.current += amount
            current_time = time.time()
            # 减少进度显示频率，提升性能
            if current_time - self.last_update > self.update_interval or self.current >= self.total:
                self._display_progress()
                self.last_update = current_time

    def _display_progress(self):
        elapsed = time.time() - self.start_time
        if self.current > 0 and elapsed > 0:
            rate = self.current / elapsed
            eta = (self.total - self.current) / rate if rate > 0 else 0

            percentage = (self.current / self.total) * 100
            bar_length = 20  # 减少进度条长度
            filled_length = int(bar_length * self.current / self.total)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)

            print(f'\r{self.description}: {bar} {percentage:.1f}% '
                  f'({self.current:,}/{self.total:,}) '
                  f'{rate:.0f}/s ETA: {eta:.0f}s', end='', flush=True)

            if self.current >= self.total:
                print(f'\n✓ {self.description} completed in {elapsed:.1f}s')


def read_file_with_mmap(file_path: str, encoding: str = 'utf-8') -> Iterator[Tuple[int, str]]:
    """使用内存映射快速读取大文件"""
    try:
        with open(file_path, 'rb') as f:
            # 对于小文件，直接读取可能更快
            file_size = os.path.getsize(file_path)
            if file_size < 1024 * 1024:  # 1MB以下直接读取
                content = f.read().decode(encoding, errors='ignore')
                for line_num, line in enumerate(content.splitlines()):
                    if line.strip():
                        yield line_num, line.strip()
                return

            # 大文件使用mmap
            with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mmapped_file:
                line_num = 0
                buffer = io.BytesIO()

                for byte in iter(lambda: mmapped_file.read(8192), b''):
                    buffer.write(byte)
                    # 处理缓冲区中的完整行
                    buffer.seek(0)
                    lines = buffer.read().split(b'\n')
                    buffer = io.BytesIO()

                    # 最后一个可能是不完整的行
                    if lines:
                        buffer.write(lines[-1])
                        for line_bytes in lines[:-1]:
                            if line_bytes.strip():
                                try:
                                    line = line_bytes.decode(encoding, errors='ignore').strip()
                                    if line:
                                        yield line_num, line
                                except:
                                    pass
                            line_num += 1

                # 处理最后的缓冲区
                final_content = buffer.getvalue()
                if final_content.strip():
                    try:
                        line = final_content.decode(encoding, errors='ignore').strip()
                        if line:
                            yield line_num, line
                    except:
                        pass

    except Exception as e:
        # 回退到普通读取
        with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
            for line_num, line in enumerate(f):
                line = line.strip()
                if line:
                    yield line_num, line


def read_single_file_optimized(file_path: str) -> Tuple[List[dict], Dict[str, List[int]], int]:
    """
    优化的单文件读取函数
    """
    file_data = []
    file_source_indices = defaultdict(list)
    error_count = 0
    batch_size = 10000  # 批处理大小
    batch_data = []

    try:
        for line_num, line in read_file_with_mmap(file_path):
            try:
                data = json.loads(line)
                if 'source' in data:
                    batch_data.append(data)

                    # 批处理，减少频繁的append操作
                    if len(batch_data) >= batch_size:
                        base_index = len(file_data)
                        for i, item in enumerate(batch_data):
                            file_source_indices[item['source']].append(base_index + i)
                        file_data.extend(batch_data)
                        batch_data = []
                else:
                    error_count += 1
                    if error_count <= 5:  # 减少错误信息输出
                        print(f"\n警告: {os.path.basename(file_path)} 第 {line_num + 1} 行缺少 'source' 字段")

            except json.JSONDecodeError:
                error_count += 1
                if error_count <= 5:
                    print(f"\n警告: {os.path.basename(file_path)} 第 {line_num + 1} 行JSON解析错误")

        # 处理剩余的批数据
        if batch_data:
            base_index = len(file_data)
            for i, item in enumerate(batch_data):
                file_source_indices[item['source']].append(base_index + i)
            file_data.extend(batch_data)

    except Exception as e:
        print(f"\n错误: 无法读取文件 {os.path.basename(file_path)}: {e}")
        return [], {}, 0

    return file_data, file_source_indices, error_count


def estimate_total_lines_fast(files: List[str], sample_ratio: float = 0.1) -> int:
    """
    快速估算总行数，使用更小的采样比例
    """
    if not files:
        return 0

    # 最多采样3个文件
    sample_count = max(1, min(3, int(len(files) * sample_ratio)))
    sample_files = random.sample(files, sample_count) if len(files) > sample_count else files

    total_lines = 0
    total_size = 0

    for file_path in sample_files:
        try:
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                continue

            # 对小文件直接计数，大文件采样估算
            if file_size < 10 * 1024 * 1024:  # 10MB以下直接计数
                line_count = sum(1 for _ in read_file_with_mmap(file_path))
            else:
                # 大文件只读前面一部分来估算
                line_count = 0
                read_bytes = 0
                target_bytes = min(1024 * 1024, file_size // 10)  # 读取1MB或文件的1/10

                with open(file_path, 'rb') as f:
                    while read_bytes < target_bytes:
                        chunk = f.read(8192)
                        if not chunk:
                            break
                        line_count += chunk.count(b'\n')
                        read_bytes += len(chunk)

                # 按比例推算整个文件的行数
                if read_bytes > 0:
                    line_count = int(line_count * file_size / read_bytes)

            total_lines += line_count
            total_size += file_size

        except Exception:
            continue

    if total_size > 0:
        # 计算所有文件的总大小
        all_files_size = sum(os.path.getsize(f) for f in files if os.path.exists(f))
        # 基于大小比例估算总行数
        estimated_lines = int((total_lines / total_size) * all_files_size)
        return max(estimated_lines, len(files) * 1000)  # 至少估算每文件1000行

    return len(files) * 5000  # 默认估算


def read_jsonl_files_parallel_optimized(file_pattern: str, max_workers: int = None) -> Tuple[
    List[dict], Dict[str, List[int]]]:
    """
    极度优化的并行读取函数
    """
    # 获取所有匹配的文件
    files = glob.glob(file_pattern)
    if not files:
        raise ValueError(f"没有找到匹配模式 '{file_pattern}' 的文件")

    # 按文件大小排序，小文件优先处理（减少内存峰值）
    files.sort(key=lambda f: os.path.getsize(f) if os.path.exists(f) else 0)

    print(f"找到 {len(files)} 个JSONL文件")

    # 快速估算总行数
    print("快速估算数据量...")
    estimated_lines = estimate_total_lines_fast(files)
    print(f"预估总行数: {estimated_lines:,}")

    # 优化并行度
    cpu_count = os.cpu_count() or 1
    if max_workers is None:
        # 根据文件数量和CPU核心数动态调整
        optimal_workers = min(len(files), cpu_count * 2, 16)  # 最多16个线程
    else:
        optimal_workers = min(max_workers, len(files), cpu_count * 2)

    print(f"使用 {optimal_workers} 个线程并行读取")

    # 创建进度跟踪器
    progress = ProgressTracker(len(files), "读取文件")

    all_data = []
    source_indices = defaultdict(list)
    total_errors = 0
    data_lock = threading.Lock()

    def process_file(file_path: str) -> Tuple[str, List[dict], Dict[str, List[int]], int]:
        """处理单个文件的包装函数"""
        result = read_single_file_optimized(file_path)
        progress.update(1)
        return file_path, result[0], result[1], result[2]

    # 使用线程池处理文件
    with ThreadPoolExecutor(max_workers=optimal_workers) as executor:
        # 提交所有任务
        futures = [executor.submit(process_file, file_path) for file_path in files]

        # 收集结果
        for future in as_completed(futures):
            try:
                file_path, file_data, file_source_indices, error_count = future.result()

                if file_data:
                    with data_lock:
                        # 调整索引偏移量
                        base_index = len(all_data)
                        for source, indices in file_source_indices.items():
                            adjusted_indices = [idx + base_index for idx in indices]
                            source_indices[source].extend(adjusted_indices)

                        all_data.extend(file_data)
                        total_errors += error_count

            except Exception as e:
                print(f"\n错误: 处理文件时发生异常: {e}")

    print(f"\n总共读取 {len(all_data):,} 条有效数据")
    if total_errors > 0:
        print(f"跳过 {total_errors} 条错误记录")

    print(f"发现 {len(source_indices)} 个不同的source:")
    for source, indices in sorted(source_indices.items()):
        print(f"  - {source}: {len(indices):,} 条数据")

    return all_data, source_indices


def load_previous_sampling_records_optimized(record_files: List[str]) -> Dict[str, Set[int]]:
    """
    优化的记录加载，使用流式处理大记录文件
    """
    sampled_indices = defaultdict(set)

    if not record_files:
        return sampled_indices

    print("加载历史采样记录...")

    for record_file in record_files:
        if not os.path.exists(record_file):
            print(f"记录文件 {record_file} 不存在，跳过")
            continue

        try:
            file_size = os.path.getsize(record_file)

            # 对于大记录文件，使用流式JSON解析
            if file_size > 50 * 1024 * 1024:  # 50MB以上使用流式处理
                print(f"大记录文件 {record_file} ({file_size // 1024 // 1024}MB)，使用流式处理...")
                # 这里可以实现流式JSON解析，或者分块读取
                # 暂时使用标准方式，但可以考虑使用ijson等库

            with open(record_file, 'r', encoding='utf-8') as f:
                record_data = json.load(f)

            if 'source_details' in record_data:
                for source, indices in record_data['source_details'].items():
                    # 批量更新，避免频繁的集合操作
                    sampled_indices[source].update(indices)

        except Exception as e:
            print(f"警告: 记录文件 {record_file} 加载失败: {e}")
            continue

    if sampled_indices:
        total_previous = sum(len(indices) for indices in sampled_indices.values())
        print(f"加载了 {total_previous:,} 条历史采样记录")

    return sampled_indices


def sample_data_optimized(all_data: List[dict], remaining_indices: Dict[str, List[int]],
                          sample_ratio: float, random_seed: int = 42) -> Tuple[List[dict], Dict[str, List[int]]]:
    """
    优化的采样函数，减少内存分配和拷贝
    """
    if not 0 < sample_ratio <= 1.0:
        raise ValueError("抽样比例必须在 0 和 1 之间")

    random.seed(random_seed)

    sampled_indices = {}
    total_to_sample = 0

    print(f"\n计算采样数量 (比例: {sample_ratio * 100:.1f}%):")

    # 预先计算所有source的采样数量
    sampling_plan = {}
    for source, indices in remaining_indices.items():
        if indices:
            sample_count = max(1, int(len(indices) * sample_ratio))
            sample_count = min(sample_count, len(indices))
            sampling_plan[source] = sample_count
            total_to_sample += sample_count
            print(f"  - {source}: {len(indices):,} -> {sample_count:,}")
        else:
            sampling_plan[source] = 0

    if total_to_sample == 0:
        print("没有数据可以采样")
        return [], {}

    # 预分配结果列表
    sampled_data = [None] * total_to_sample
    current_idx = 0

    print(f"\n开始采样，总计 {total_to_sample:,} 条数据...")
    progress = ProgressTracker(len(sampling_plan), "采样进度")

    for source, indices in remaining_indices.items():
        sample_count = sampling_plan[source]
        if sample_count == 0:
            sampled_indices[source] = []
            progress.update(1)
            continue

        # 随机抽样
        sampled_source_indices = random.sample(indices, sample_count)
        sampled_source_indices.sort()

        # 批量填充结果数组
        for i, idx in enumerate(sampled_source_indices):
            sampled_data[current_idx + i] = all_data[idx]

        sampled_indices[source] = sampled_source_indices
        current_idx += sample_count
        progress.update(1)

    print(f"\n采样完成，共 {total_to_sample:,} 条数据")
    return sampled_data, sampled_indices


def split_data_into_files_optimized(sampled_data: List[dict], output_prefix: str,
                                    max_lines_per_file: int = 10000) -> List[str]:
    """
    优化的文件分割，使用更大的写入批次
    """
    if not sampled_data:
        return []

    num_files = math.ceil(len(sampled_data) / max_lines_per_file)
    output_files = []

    print(f"\n分割为 {num_files} 个文件...")
    progress = ProgressTracker(num_files, "写入文件")

    for file_idx in range(num_files):
        start_idx = file_idx * max_lines_per_file
        end_idx = min(start_idx + max_lines_per_file, len(sampled_data))

        if num_files == 1:
            output_file = f"{output_prefix}.jsonl"
        else:
            output_file = f"{output_prefix}_part{file_idx + 1:03d}.jsonl"

        # 使用更大的批次写入
        batch_size = 5000
        with open(output_file, 'w', encoding='utf-8', buffering=1024 * 1024) as f:  # 1MB缓冲区
            for batch_start in range(start_idx, end_idx, batch_size):
                batch_end = min(batch_start + batch_size, end_idx)

                # 批量构建JSON字符串
                json_lines = [json.dumps(sampled_data[i], ensure_ascii=False, separators=(',', ':'))
                              for i in range(batch_start, batch_end)]

                # 一次性写入
                f.write('\n'.join(json_lines) + '\n')

        output_files.append(output_file)
        progress.update(1)

    return output_files


def main():
    parser = argparse.ArgumentParser(description='极速JSONL文件采样工具')
    parser.add_argument('--input', '-i', default='*.jsonl',
                        help='输入文件匹配模式 (默认: *.jsonl)')
    parser.add_argument('--output', '-o', default='sampled_data',
                        help='输出文件前缀 (默认: sampled_data)')
    parser.add_argument('--ratio', '-r', type=float, default=0.1,
                        help='抽样比例 (默认: 0.1 即10%%)')
    parser.add_argument('--seed', '-s', type=int, default=42,
                        help='随机种子 (默认: 42)')
    parser.add_argument('--record', default='sampling_record.json',
                        help='本次索引记录文件名 (默认: sampling_record.json)')
    parser.add_argument('--previous-records', nargs='*', default=[],
                        help='之前的采样记录文件路径列表')
    parser.add_argument('--max-lines', type=int, default=10000,
                        help='每个输出文件的最大行数 (默认: 10,000)')
    parser.add_argument('--max-workers', type=int, default=None,
                        help='并行读取的最大线程数 (默认: 自动)')

    args = parser.parse_args()

    if not 0 < args.ratio <= 1.0:
        print("错误: 抽样比例必须在 0 和 1 之间")
        return

    start_time = time.time()

    try:
        # 读取数据
        print("=" * 60)
        print("第一步: 极速并行读取")
        print("=" * 60)
        all_data, source_indices = read_jsonl_files_parallel_optimized(args.input, args.max_workers)

        if not all_data:
            print("错误: 没有找到有效的数据")
            return

        # 加载记录
        print("\n" + "=" * 60)
        print("第二步: 加载历史记录")
        print("=" * 60)
        previous_sampled = load_previous_sampling_records_optimized(args.previous_records)

        # 计算剩余数据
        print("\n" + "=" * 60)
        print("第三步: 计算剩余数据")
        print("=" * 60)

        remaining_indices = {}
        for source, all_indices in source_indices.items():
            sampled_set = previous_sampled.get(source, set())
            if not sampled_set:
                remaining = all_indices
            else:
                remaining = [idx for idx in all_indices if idx not in sampled_set]
            remaining_indices[source] = remaining
            print(f"  - {source}: {len(all_indices):,} 总数据, "
                  f"{len(sampled_set):,} 已采样, {len(remaining):,} 剩余")

        total_remaining = sum(len(indices) for indices in remaining_indices.values())
        if total_remaining == 0:
            print("警告: 没有剩余数据可以采样")
            return

        # 采样
        print("\n" + "=" * 60)
        print("第四步: 高速采样")
        print("=" * 60)
        sampled_data, sampled_indices = sample_data_optimized(
            all_data, remaining_indices, args.ratio, args.seed
        )

        if not sampled_data:
            print("没有采样到任何数据")
            return

        # 保存文件
        print("\n" + "=" * 60)
        print("第五步: 高速写入")
        print("=" * 60)
        output_files = split_data_into_files_optimized(sampled_data, args.output, args.max_lines)

        # 保存记录
        record_data = {
            'timestamp': datetime.now().isoformat(),
            'sample_ratio': args.ratio,
            'random_seed': args.seed,
            'total_sampled': len(sampled_data),
            'output_files': output_files,
            'source_details': sampled_indices
        }

        with open(args.record, 'w', encoding='utf-8') as f:
            json.dump(record_data, f, ensure_ascii=False, indent=2)

        # 统计信息
        total_time = time.time() - start_time
        print("\n" + "=" * 60)
        print("🚀 极速采样完成!")
        print("=" * 60)
        print(f"⏱️  总耗时: {total_time:.2f}秒")
        print(f"🔥 处理速度: {len(all_data) / total_time:.0f} 条/秒")
        print(f"📊 原始数据: {len(all_data):,} 条")
        print(f"📈 本次采样: {len(sampled_data):,} 条")
        print(f"📁 输出文件: {len(output_files)} 个")

        if total_time > 60:
            minutes = int(total_time // 60)
            seconds = total_time % 60
            print(f"⏱️  详细耗时: {minutes}分{seconds:.1f}秒")

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()