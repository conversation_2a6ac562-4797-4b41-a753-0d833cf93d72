#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import argparse
from pathlib import Path
from typing import Dict, List, Optional

try:
    from tqdm import tqdm

    HAS_TQDM = True
except ImportError:
    HAS_TQDM = False
    print("提示: 安装 tqdm 库可以显示进度条 (pip install tqdm)")


def extract_filename_from_path(pdf_path: str) -> str:
    """
    从PDF路径中提取文件名（不含扩展名）
    """
    return Path(pdf_path).stem


def find_matching_files(base_dir: str, pdf_filename: str) -> Dict[str, Optional[str]]:
    """
    在指定目录下查找匹配的MD文件和chunk JSON文件

    Args:
        base_dir: 搜索的基础目录
        pdf_filename: PDF文件名（不含扩展名）

    Returns:
        包含md_path和chunk_path的字典
    """
    base_path = Path(base_dir)
    result = {"md_path": None, "chunk_path": None}

    # 搜索所有可能的子目录
    for root, dirs, files in os.walk(base_path):
        root_path = Path(root)

        # 查找MD文件
        for file in files:
            if file.endswith('.md') and pdf_filename in file:
                result["md_path"] = str((root_path / file).resolve())
                break

        # 查找chunk JSON文件
        for file in files:
            if file.endswith('_chunk.json') and pdf_filename in file:
                result["chunk_path"] = str((root_path / file).resolve())
                break

        # 如果两个文件都找到了，可以提前退出
        if result["md_path"] and result["chunk_path"]:
            break

    return result


def count_lines(file_path: str) -> int:
    """
    统计文件行数
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return sum(1 for line in f if line.strip())
    except Exception:
        return 0


def process_jsonl_file(input_file: str, output_file: str, search_dir: str) -> None:
    """
    处理输入的JSONL文件，查找匹配的文件并生成输出

    Args:
        input_file: 输入的JSONL文件路径
        output_file: 输出的JSONL文件路径
        search_dir: 搜索目录
    """
    matched_count = 0
    total_count = 0

    # 统计总行数用于进度条
    total_lines = count_lines(input_file)
    print(f"准备处理 {total_lines} 个文件...")

    with open(input_file, 'r', encoding='utf-8') as infile, \
            open(output_file, 'w', encoding='utf-8') as outfile:

        # 创建进度条
        if HAS_TQDM:
            pbar = tqdm(total=total_lines, desc="处理文件", unit="个")

        for line in infile:
            line = line.strip()
            if not line:
                continue

            try:
                data = json.loads(line)
                total_count += 1

                pdf_path = data.get('raw_content', '')
                if not pdf_path:
                    if not HAS_TQDM:
                        print(f"警告: 第{total_count}行没有raw_content字段")
                    if HAS_TQDM:
                        pbar.set_postfix({"警告": f"第{total_count}行无raw_content"})
                        pbar.update(1)
                    continue

                # 提取PDF文件名
                pdf_filename = extract_filename_from_path(pdf_path)

                # 更新进度条显示当前处理的文件
                if HAS_TQDM:
                    pbar.set_description(f"处理: {pdf_filename[:30]}...")
                else:
                    print(f"处理文件 {total_count}/{total_lines}: {pdf_filename}")

                # 查找匹配的文件
                matched_files = find_matching_files(search_dir, pdf_filename)

                # 如果找到了MD文件或chunk文件，则生成输出
                if matched_files["md_path"] or matched_files["chunk_path"]:
                    output_data = {
                        "raw_content": pdf_path
                    }

                    # 只有找到文件时才添加对应字段
                    if matched_files["md_path"]:
                        output_data["text_path"] = matched_files["md_path"]
                    if matched_files["chunk_path"]:
                        output_data["chunk_path"] = matched_files["chunk_path"]

                    # 写入输出文件
                    json.dump(output_data, outfile, ensure_ascii=False)
                    outfile.write('\n')
                    matched_count += 1

                    # 更新进度条状态
                    if HAS_TQDM:
                        pbar.set_postfix({"匹配": f"{matched_count}/{total_count}"})
                    else:
                        print(f"  ✓ 找到匹配文件 ({matched_count}/{total_count})")
                        if matched_files["md_path"]:
                            print(f"    MD: {matched_files['md_path']}")
                        if matched_files["chunk_path"]:
                            print(f"    Chunk: {matched_files['chunk_path']}")
                else:
                    if not HAS_TQDM:
                        print(f"  ✗ 未找到匹配文件")

                # 更新进度条
                if HAS_TQDM:
                    pbar.update(1)

            except json.JSONDecodeError as e:
                error_msg = f"第{total_count}行JSON解析失败"
                if HAS_TQDM:
                    pbar.set_postfix({"错误": error_msg})
                    pbar.update(1)
                else:
                    print(f"错误: {error_msg}: {e}")
                continue
            except Exception as e:
                error_msg = f"第{total_count}行处理异常"
                if HAS_TQDM:
                    pbar.set_postfix({"错误": error_msg})
                    pbar.update(1)
                else:
                    print(f"错误: {error_msg}: {e}")
                continue

        # 关闭进度条
        if HAS_TQDM:
            pbar.close()

    print(f"\n处理完成:")
    print(f"总共处理: {total_count} 个文件")
    print(f"找到匹配: {matched_count} 个文件")
    print(f"匹配率: {matched_count / total_count * 100:.1f}%" if total_count > 0 else "匹配率: 0%")
    print(f"输出文件: {output_file}")


def main():
    parser = argparse.ArgumentParser(description='处理PDF文件路径并查找对应的MD和chunk JSON文件')
    parser.add_argument('input_file', help='输入的JSONL文件路径')
    parser.add_argument('-o', '--output', default='output.jsonl', help='输出的JSONL文件路径 (默认: output.jsonl)')
    parser.add_argument('-d', '--search-dir',
                        default='/home/<USER>/open-dataflow/example_data/KBCleaningPipeline/part_aa/',
                        help='搜索目录 (默认: /home/<USER>/open-dataflow/example_data/KBCleaningPipeline/part_aa/)')

    args = parser.parse_args()

    # 检查输入文件是否存在
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件不存在: {args.input_file}")
        return

    # 检查搜索目录是否存在
    if not os.path.exists(args.search_dir):
        print(f"错误: 搜索目录不存在: {args.search_dir}")
        return

    print(f"输入文件: {args.input_file}")
    print(f"搜索目录: {args.search_dir}")
    print(f"输出文件: {args.output}")
    print("-" * 50)

    # 处理文件
    process_jsonl_file(args.input_file, args.output, args.search_dir)


if __name__ == "__main__":
    main()
