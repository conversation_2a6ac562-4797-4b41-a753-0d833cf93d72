"""
📦 安装OCR依赖：
Ubuntu/Debian:
bash
# 安装Tesseract OCR
sudo apt update
sudo apt install tesseract-ocr tesseract-ocr-chi-sim tesseract-ocr-chi-tra

# Python包
pip install pytesseract pillow easyocr torch torchvision
CentOS/RHEL:
bash
# 安装Tesseract
sudo yum install epel-release
sudo yum install tesseract tesseract-langpack-chi_sim tesseract-langpack-chi_tra

# Python包
pip install pytesseract pillow easyocr
"""

import os
import json
import time
import multiprocessing as mp
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor, as_completed
import threading
from collections import defaultdict
import gc

# 语言检测库 - 简化导入
try:
    from langdetect import detect
    import langdetect

    langdetect.DetectorFactory.seed = 0


    # 简单的异常处理方式
    def safe_detect(text):
        try:
            return detect(text)
        except:
            return "unknown"

except ImportError:
    print("请安装 langdetect: pip install langdetect")
    exit(1)

# PDF处理库
try:
    import pymupdf
except ImportError:
    print("请安装 PyMuPDF: pip install pymupdf")
    exit(1)


class SimplePDFDetector:
    def __init__(self, source_dir, output_file):
        self.source_dir = Path(source_dir)
        self.output_file = output_file
        self.processed_count = 0
        self.failed_count = 0
        self.lock = threading.Lock()

        # 针对192核的配置
        self.max_workers = 96
        self.batch_size = 20000
        self.sample_pages = 3
        self.sample_chars = 3000
        self.timeout = 30

    def extract_text_simple(self, pdf_path):
        """简单文本提取"""
        try:
            doc = pymupdf.open(pdf_path)
            text = ""

            max_pages = min(len(doc), self.sample_pages)

            for page_num in range(max_pages):
                page = doc[page_num]
                page_text = page.get_text()
                text += page_text

                if len(text) >= self.sample_chars:
                    break

            doc.close()
            return text[:self.sample_chars]

        except Exception:
            return None

    def detect_language_simple(self, text):
        """简单语言检测"""
        if not text or len(text.strip()) < 50:
            return "unknown", 0.0

        clean_text = ' '.join(text.split())
        lang = safe_detect(clean_text)
        confidence = min(1.0, len(clean_text) / 1000.0)

        return lang, confidence

    def process_single_pdf_simple(self, pdf_path):
        """简单PDF处理"""
        try:
            text = self.extract_text_simple(pdf_path)

            if not text:
                return {
                    "filename": pdf_path.name,
                    "primary_language": "empty",
                    "confidence": 0.0,
                    "error": "无法提取文本"
                }

            language, confidence = self.detect_language_simple(text)

            return {
                "filename": pdf_path.name,
                "primary_language": language,
                "confidence": confidence,
                "text_length": len(text),
                "error": None
            }

        except Exception as e:
            return {
                "filename": pdf_path.name,
                "primary_language": "error",
                "confidence": 0.0,
                "error": str(e)
            }

    def get_pdf_files_fast(self):
        """快速获取PDF文件列表"""
        print("扫描PDF文件...")
        pdf_files = []

        def scan_dir(directory):
            try:
                with os.scandir(directory) as entries:
                    for entry in entries:
                        if entry.is_file() and entry.name.lower().endswith('.pdf'):
                            pdf_files.append(Path(entry.path))
                        elif entry.is_dir():
                            scan_dir(entry.path)
            except PermissionError:
                pass

        scan_dir(self.source_dir)
        print(f"找到 {len(pdf_files)} 个PDF文件")
        return pdf_files

    def process_batch(self):
        """批量处理"""
        pdf_files = self.get_pdf_files_fast()

        if not pdf_files:
            print("没有找到PDF文件")
            return []

        start_time = time.time()
        all_results = []

        print(f"开始处理，配置: {self.max_workers}进程, 批次大小{self.batch_size}")

        for batch_num, i in enumerate(range(0, len(pdf_files), self.batch_size)):
            batch = pdf_files[i:i + self.batch_size]
            batch_start = time.time()

            print(f"\n=== 第 {batch_num + 1} 批次 ===")
            print(f"处理 {len(batch)} 个文件")

            batch_results = []

            with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_file = {
                    executor.submit(self.process_single_pdf_simple, pdf_path): pdf_path
                    for pdf_path in batch
                }

                completed = 0
                for future in as_completed(future_to_file):
                    try:
                        result = future.result(timeout=self.timeout)
                        batch_results.append(result)
                        completed += 1

                        if completed % 1000 == 0:
                            elapsed = time.time() - batch_start
                            rate = completed / elapsed
                            print(f"  批次进度: {completed}/{len(batch)}, 速度: {rate:.1f}/秒")

                    except Exception as e:
                        pdf_path = future_to_file[future]
                        error_result = {
                            "filename": pdf_path.name,
                            "primary_language": "timeout",
                            "confidence": 0.0,
                            "error": f"处理超时: {str(e)}"
                        }
                        batch_results.append(error_result)

            all_results.extend(batch_results)

            # 保存进度
            self.save_results(all_results)

            # 批次统计
            batch_elapsed = time.time() - batch_start
            batch_rate = len(batch) / batch_elapsed
            print(f"批次完成: 耗时 {batch_elapsed:.1f}秒, 速度 {batch_rate:.1f}/秒")

            # 清理内存
            gc.collect()

            # 总进度
            total_elapsed = time.time() - start_time
            total_rate = len(all_results) / total_elapsed
            remaining = len(pdf_files) - len(all_results)
            eta_hours = (remaining / total_rate) / 3600 if total_rate > 0 else 0

            print(f"总进度: {len(all_results)}/{len(pdf_files)} ({len(all_results) / len(pdf_files) * 100:.1f}%)")
            print(f"总体速度: {total_rate:.1f}/秒, 预计剩余: {eta_hours:.2f}小时")

        return all_results

    def save_results(self, results):
        """保存结果"""
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存失败: {e}")

    def analyze_results(self, results):
        """分析结果"""
        if not results:
            return

        lang_count = defaultdict(int)
        error_count = 0

        for result in results:
            if result["primary_language"] in ["error", "timeout", "empty"]:
                error_count += 1
            else:
                lang_count[result["primary_language"]] += 1

        print("\n" + "=" * 50)
        print("统计结果")
        print("=" * 50)
        print(f"总文件数: {len(results):,}")
        print(f"处理成功: {len(results) - error_count:,}")
        print(f"处理失败: {error_count:,}")
        print(f"成功率: {(len(results) - error_count) / len(results) * 100:.2f}%")

        print(f"\n语言分布 (前10名):")
        for lang, count in sorted(lang_count.items(), key=lambda x: x[1], reverse=True)[:10]:
            percentage = count / len(results) * 100
            print(f"  {lang}: {count:,} 个文件 ({percentage:.2f}%)")


def main_simple():
    """简单主函数"""
    source_dir = "/home/<USER>/datasets/联通项目通信专利"
    output_file = "./pdf_language_simple_results.json"

    print("=" * 50)
    print("PDF语言检测 (简化版)")
    print("=" * 50)
    print(f"CPU核心数: {mp.cpu_count()}")

    detector = SimplePDFDetector(source_dir, output_file)

    start_time = time.time()
    results = detector.process_batch()
    elapsed_hours = (time.time() - start_time) / 3600

    detector.analyze_results(results)

    print(f"\n总耗时: {elapsed_hours:.2f} 小时")
    if results:
        print(f"平均速度: {len(results) / (elapsed_hours * 3600):.2f} 个/秒")
    print(f"结果已保存到: {output_file}")


if __name__ == "__main__":
    main_simple()