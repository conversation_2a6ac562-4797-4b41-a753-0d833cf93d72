#!/usr/bin/env python3
"""
🚀 Spark集群部署和管理工具

功能：
1. 自动安装Spark
2. 集群配置管理
3. 服务启动/停止
4. 健康检查
5. 性能监控
"""

import os
import sys
import subprocess
import time
import json
import argparse
import shutil
import urllib.request
from pathlib import Path
from typing import Dict, List, Optional, Tuple


class SparkInstaller:
    """Spark安装器"""
    
    def __init__(self, spark_version: str = "3.5.0", hadoop_version: str = "3"):
        self.spark_version = spark_version
        self.hadoop_version = hadoop_version
        self.spark_home = Path.home() / f"spark-{spark_version}-bin-hadoop{hadoop_version}"
        self.download_url = self._get_download_url()
        
    def _get_download_url(self) -> str:
        """获取下载URL"""
        base_url = "https://archive.apache.org/dist/spark"
        filename = f"spark-{self.spark_version}-bin-hadoop{self.hadoop_version}.tgz"
        return f"{base_url}/spark-{self.spark_version}/{filename}"
        
    def check_installation(self) -> bool:
        """检查Spark是否已安装"""
        spark_submit = self.spark_home / "bin" / "spark-submit"
        return spark_submit.exists()
        
    def install_spark(self) -> bool:
        """安装Spark"""
        if self.check_installation():
            print(f"✅ Spark {self.spark_version} 已安装在: {self.spark_home}")
            return True
            
        print(f"📦 开始安装Spark {self.spark_version}...")
        
        try:
            # 下载Spark
            download_path = Path.home() / f"spark-{self.spark_version}.tgz"
            print(f"⬇️ 下载Spark: {self.download_url}")
            
            urllib.request.urlretrieve(self.download_url, download_path)
            print(f"✅ 下载完成: {download_path}")
            
            # 解压
            print("📂 解压Spark...")
            subprocess.run([
                "tar", "-xzf", str(download_path), "-C", str(Path.home())
            ], check=True)
            
            # 清理下载文件
            download_path.unlink()
            
            # 验证安装
            if self.check_installation():
                print(f"✅ Spark安装成功: {self.spark_home}")
                return True
            else:
                print("❌ Spark安装失败")
                return False
                
        except Exception as e:
            print(f"❌ Spark安装失败: {e}")
            return False
            
    def setup_environment(self):
        """设置环境变量"""
        env_vars = {
            'SPARK_HOME': str(self.spark_home),
            'PATH': f"{self.spark_home}/bin:{os.environ.get('PATH', '')}",
            'PYSPARK_PYTHON': sys.executable
        }
        
        # 更新当前环境
        for key, value in env_vars.items():
            os.environ[key] = value
            
        # 生成环境配置脚本
        env_script = Path("spark_env.sh")
        with open(env_script, 'w') as f:
            f.write("#!/bin/bash\n")
            f.write("# Spark环境配置\n\n")
            for key, value in env_vars.items():
                f.write(f"export {key}='{value}'\n")
                
        env_script.chmod(0o755)
        print(f"✅ 环境配置已保存到: {env_script}")
        
        return env_vars


class SparkClusterManager:
    """Spark集群管理器"""
    
    def __init__(self, spark_home: Path):
        self.spark_home = spark_home
        self.conf_dir = spark_home / "conf"
        self.sbin_dir = spark_home / "sbin"
        
    def create_cluster_config(self, master_host: str, worker_hosts: List[str], 
                            config: Dict) -> bool:
        """创建集群配置"""
        try:
            # 创建spark-defaults.conf
            defaults_conf = self.conf_dir / "spark-defaults.conf"
            with open(defaults_conf, 'w') as f:
                f.write("# Spark默认配置\n")
                for key, value in config.items():
                    spark_key = f"spark.{key.replace('_', '.')}"
                    f.write(f"{spark_key} {value}\n")
                    
            # 创建slaves文件 (Spark 3.0+使用workers)
            workers_file = self.conf_dir / "workers"
            with open(workers_file, 'w') as f:
                for worker in worker_hosts:
                    f.write(f"{worker}\n")
                    
            # 创建spark-env.sh
            env_file = self.conf_dir / "spark-env.sh"
            with open(env_file, 'w') as f:
                f.write("#!/usr/bin/env bash\n")
                f.write(f"export SPARK_MASTER_HOST={master_host}\n")
                f.write("export SPARK_MASTER_PORT=7077\n")
                f.write("export SPARK_MASTER_WEBUI_PORT=8080\n")
                f.write("export SPARK_WORKER_WEBUI_PORT=8081\n")
                f.write(f"export SPARK_WORKER_MEMORY={config.get('executor_memory', '4g')}\n")
                f.write(f"export SPARK_WORKER_CORES={config.get('executor_cores', '2')}\n")
                
            env_file.chmod(0o755)
            
            print(f"✅ 集群配置已创建")
            print(f"   - Master: {master_host}:7077")
            print(f"   - Workers: {len(worker_hosts)} 个节点")
            
            return True
            
        except Exception as e:
            print(f"❌ 集群配置创建失败: {e}")
            return False
            
    def start_master(self) -> bool:
        """启动Master节点"""
        try:
            start_master_script = self.sbin_dir / "start-master.sh"
            result = subprocess.run([str(start_master_script)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Spark Master已启动")
                print("   - Web UI: http://localhost:8080")
                return True
            else:
                print(f"❌ Master启动失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Master启动失败: {e}")
            return False
            
    def start_workers(self) -> bool:
        """启动Worker节点"""
        try:
            start_workers_script = self.sbin_dir / "start-workers.sh"
            result = subprocess.run([str(start_workers_script)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Spark Workers已启动")
                return True
            else:
                print(f"❌ Workers启动失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Workers启动失败: {e}")
            return False
            
    def stop_cluster(self) -> bool:
        """停止集群"""
        try:
            stop_all_script = self.sbin_dir / "stop-all.sh"
            result = subprocess.run([str(stop_all_script)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Spark集群已停止")
                return True
            else:
                print(f"⚠️ 集群停止可能不完整: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 集群停止失败: {e}")
            return False
            
    def check_cluster_status(self) -> Dict:
        """检查集群状态"""
        status = {
            'master_running': False,
            'workers_running': 0,
            'master_url': None,
            'web_ui_url': None
        }
        
        try:
            # 检查Master进程
            result = subprocess.run(['jps'], capture_output=True, text=True)
            if 'Master' in result.stdout:
                status['master_running'] = True
                status['master_url'] = 'spark://localhost:7077'
                status['web_ui_url'] = 'http://localhost:8080'
                
            # 统计Worker进程
            worker_count = result.stdout.count('Worker')
            status['workers_running'] = worker_count
            
        except Exception as e:
            print(f"⚠️ 状态检查失败: {e}")
            
        return status


class SparkHealthChecker:
    """Spark健康检查器"""
    
    def __init__(self, spark_home: Path):
        self.spark_home = spark_home
        
    def run_health_check(self) -> bool:
        """运行健康检查"""
        print("🔍 开始Spark健康检查...")
        
        checks = [
            self._check_spark_installation,
            self._check_java_installation,
            self._check_python_installation,
            self._check_pyspark_import,
            self._run_simple_job
        ]
        
        passed = 0
        for check in checks:
            if check():
                passed += 1
                
        success_rate = (passed / len(checks)) * 100
        print(f"\n📊 健康检查结果: {passed}/{len(checks)} 通过 ({success_rate:.1f}%)")
        
        return success_rate >= 80
        
    def _check_spark_installation(self) -> bool:
        """检查Spark安装"""
        try:
            spark_submit = self.spark_home / "bin" / "spark-submit"
            if spark_submit.exists():
                print("✅ Spark安装检查通过")
                return True
            else:
                print("❌ Spark未正确安装")
                return False
        except Exception as e:
            print(f"❌ Spark安装检查失败: {e}")
            return False
            
    def _check_java_installation(self) -> bool:
        """检查Java安装"""
        try:
            result = subprocess.run(['java', '-version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Java安装检查通过")
                return True
            else:
                print("❌ Java未安装或配置错误")
                return False
        except Exception as e:
            print(f"❌ Java检查失败: {e}")
            return False
            
    def _check_python_installation(self) -> bool:
        """检查Python安装"""
        try:
            version = sys.version_info
            if version.major >= 3 and version.minor >= 7:
                print(f"✅ Python版本检查通过: {version.major}.{version.minor}")
                return True
            else:
                print(f"❌ Python版本过低: {version.major}.{version.minor}")
                return False
        except Exception as e:
            print(f"❌ Python检查失败: {e}")
            return False
            
    def _check_pyspark_import(self) -> bool:
        """检查PySpark导入"""
        try:
            import pyspark
            print(f"✅ PySpark导入检查通过: {pyspark.__version__}")
            return True
        except ImportError:
            print("❌ PySpark导入失败")
            return False
        except Exception as e:
            print(f"❌ PySpark检查失败: {e}")
            return False
            
    def _run_simple_job(self) -> bool:
        """运行简单测试任务"""
        try:
            from pyspark.sql import SparkSession
            
            spark = SparkSession.builder \
                .appName("HealthCheck") \
                .master("local[2]") \
                .getOrCreate()
                
            # 创建简单DataFrame
            data = [("Alice", 25), ("Bob", 30), ("Charlie", 35)]
            df = spark.createDataFrame(data, ["name", "age"])
            
            # 执行简单操作
            count = df.count()
            avg_age = df.agg({"age": "avg"}).collect()[0][0]
            
            spark.stop()
            
            if count == 3 and 25 <= avg_age <= 35:
                print("✅ Spark任务测试通过")
                return True
            else:
                print("❌ Spark任务测试失败")
                return False
                
        except Exception as e:
            print(f"❌ Spark任务测试失败: {e}")
            return False


def main():
    parser = argparse.ArgumentParser(description='🚀 Spark集群部署和管理工具')
    parser.add_argument('action', choices=['install', 'configure', 'start', 'stop', 'status', 'health'],
                        help='执行的操作')
    parser.add_argument('--spark-version', default='3.5.0',
                        help='Spark版本 (默认: 3.5.0)')
    parser.add_argument('--hadoop-version', default='3',
                        help='Hadoop版本 (默认: 3)')
    parser.add_argument('--master-host', default='localhost',
                        help='Master节点主机名')
    parser.add_argument('--worker-hosts', nargs='*', default=['localhost'],
                        help='Worker节点主机名列表')
    parser.add_argument('--config-file', 
                        help='Spark配置文件路径')
    
    args = parser.parse_args()
    
    print("🚀 Spark集群部署和管理工具")
    print("=" * 50)
    
    if args.action == 'install':
        # 安装Spark
        installer = SparkInstaller(args.spark_version, args.hadoop_version)
        
        if installer.install_spark():
            env_vars = installer.setup_environment()
            print("\n✅ Spark安装完成!")
            print("请运行以下命令设置环境变量:")
            print("source spark_env.sh")
        else:
            print("❌ Spark安装失败")
            sys.exit(1)
            
    elif args.action == 'configure':
        # 配置集群
        spark_home = Path.home() / f"spark-{args.spark_version}-bin-hadoop{args.hadoop_version}"
        
        if not spark_home.exists():
            print("❌ Spark未安装，请先运行: python spark_deployment.py install")
            sys.exit(1)
            
        # 加载配置
        if args.config_file and os.path.exists(args.config_file):
            with open(args.config_file, 'r') as f:
                config = json.load(f)
        else:
            # 默认配置
            config = {
                'executor_memory': '4g',
                'driver_memory': '2g',
                'executor_cores': '2',
                'default_parallelism': '8'
            }
            
        manager = SparkClusterManager(spark_home)
        if manager.create_cluster_config(args.master_host, args.worker_hosts, config):
            print("✅ 集群配置完成")
        else:
            print("❌ 集群配置失败")
            sys.exit(1)
            
    elif args.action == 'start':
        # 启动集群
        spark_home = Path.home() / f"spark-{args.spark_version}-bin-hadoop{args.hadoop_version}"
        manager = SparkClusterManager(spark_home)
        
        if manager.start_master():
            time.sleep(3)  # 等待Master启动
            if manager.start_workers():
                print("✅ Spark集群启动完成")
                print("   - Master Web UI: http://localhost:8080")
            else:
                print("⚠️ Workers启动失败")
        else:
            print("❌ 集群启动失败")
            
    elif args.action == 'stop':
        # 停止集群
        spark_home = Path.home() / f"spark-{args.spark_version}-bin-hadoop{args.hadoop_version}"
        manager = SparkClusterManager(spark_home)
        manager.stop_cluster()
        
    elif args.action == 'status':
        # 检查状态
        spark_home = Path.home() / f"spark-{args.spark_version}-bin-hadoop{args.hadoop_version}"
        manager = SparkClusterManager(spark_home)
        status = manager.check_cluster_status()
        
        print("📊 Spark集群状态:")
        print(f"   Master运行: {'✅' if status['master_running'] else '❌'}")
        print(f"   Worker数量: {status['workers_running']}")
        if status['master_url']:
            print(f"   Master URL: {status['master_url']}")
        if status['web_ui_url']:
            print(f"   Web UI: {status['web_ui_url']}")
            
    elif args.action == 'health':
        # 健康检查
        spark_home = Path.home() / f"spark-{args.spark_version}-bin-hadoop{args.hadoop_version}"
        checker = SparkHealthChecker(spark_home)
        
        if checker.run_health_check():
            print("✅ Spark环境健康")
        else:
            print("⚠️ Spark环境存在问题")


if __name__ == "__main__":
    main()
