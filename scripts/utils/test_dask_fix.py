#!/usr/bin/env python3
"""
🔧 Dask配置修复测试脚本
测试修复后的Dask配置是否正常工作
"""

import os
import sys
import time
import json
import tempfile
from pathlib import Path

try:
    import dask
    import dask.bag as db
    from dask.distributed import Client
    print(f"✅ Dask导入成功: {dask.__version__}")
except ImportError as e:
    print(f"❌ Dask未安装: {e}")
    sys.exit(1)


def test_dask_config():
    """测试Dask配置"""
    print("🔧 测试Dask配置...")
    
    # 设置安全的配置
    dask.config.set({
        'distributed.worker.daemon': False,
        'distributed.admin.tick.limit': '3s',
        'distributed.comm.retry.delay.min': '100ms',
        'distributed.comm.retry.delay.max': '20s',
        'distributed.comm.timeouts.connect': '30s',
        'distributed.comm.timeouts.tcp': '30s',
        'distributed.worker.memory.target': 0.6,
        'distributed.worker.memory.spill': 0.7,
        'distributed.worker.memory.pause': 0.8,
        'distributed.scheduler.allowed-failures': 3,
        'array.chunk-size': '128MB',
        'dataframe.query-planning': False,
    })
    
    print("✅ 配置设置完成")


def test_local_client():
    """测试本地客户端"""
    print("🧪 测试本地Dask客户端...")
    
    try:
        # 使用更保守的配置
        with Client(
            processes=False,  # 使用线程模式避免进程间通信问题
            threads_per_worker=2,
            n_workers=2,
            memory_limit='2GB',
            silence_logs=False,
            dashboard_address=':8787'
        ) as client:
            print(f"✅ 客户端创建成功")
            print(f"   Dashboard: {client.dashboard_link}")
            
            # 等待Worker就绪
            client.wait_for_workers(n_workers=2, timeout=10)
            
            # 获取集群信息
            info = client.scheduler_info()
            workers = info.get('workers', {})
            print(f"   Workers: {len(workers)}")
            
            # 简单计算测试
            print("🔄 执行计算测试...")
            data = list(range(1000))
            bag = db.from_sequence(data, partition_size=100)
            result = bag.map(lambda x: x * 2).sum().compute()
            expected = sum(x * 2 for x in data)
            
            if result == expected:
                print(f"✅ 计算测试通过: {result}")
                return True
            else:
                print(f"❌ 计算测试失败: {result} != {expected}")
                return False
                
    except Exception as e:
        print(f"❌ 客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_jsonl_processing():
    """测试JSONL处理"""
    print("📄 测试JSONL处理...")
    
    # 创建测试数据
    test_data = []
    for i in range(100):
        test_data.append({
            'id': f'test_{i}',
            'source': f'source_{i % 3}',
            'content': f'测试内容 {i}',
            'value': i
        })
    
    # 写入临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
        for item in test_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
        temp_file = f.name
    
    try:
        with Client(processes=False, threads_per_worker=2, n_workers=2, silence_logs=False) as client:
            print(f"✅ 客户端创建成功")
            
            # 读取JSONL文件
            print("📖 读取JSONL文件...")
            bag = db.read_text(temp_file)
            
            # 解析JSON
            def parse_json_safe(line):
                line = line.strip()
                if not line:
                    return None
                try:
                    return json.loads(line)
                except:
                    return None
            
            parsed_bag = bag.map(parse_json_safe).filter(lambda x: x is not None)
            
            # 计算总数
            total_count = parsed_bag.count().compute()
            print(f"📊 总记录数: {total_count}")
            
            # 按source分组统计
            source_counts = parsed_bag.pluck('source').frequencies().compute()
            print(f"📈 Source分布: {dict(source_counts)}")
            
            # 采样测试
            sample_ratio = 0.3
            sample_count = max(1, int(total_count * sample_ratio))
            sampled_data = parsed_bag.take(sample_count)
            
            print(f"🎯 采样结果: {len(sampled_data)}/{total_count} 条")
            
            if len(sampled_data) > 0:
                print("✅ JSONL处理测试通过")
                return True
            else:
                print("❌ JSONL处理测试失败")
                return False
                
    except Exception as e:
        print(f"❌ JSONL处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_file)
        except:
            pass


def test_error_recovery():
    """测试错误恢复"""
    print("🔄 测试错误恢复...")
    
    try:
        with Client(processes=False, threads_per_worker=1, n_workers=1, silence_logs=False) as client:
            print("✅ 客户端创建成功")
            
            # 测试正常操作
            data = list(range(50))
            bag = db.from_sequence(data, partition_size=10)
            result1 = bag.sum().compute()
            print(f"✅ 正常计算: {result1}")
            
            # 测试错误处理
            def error_func(x):
                if x == 25:
                    raise ValueError("测试错误")
                return x * 2
            
            try:
                error_bag = bag.map(error_func)
                result2 = error_bag.sum().compute()
                print(f"❌ 错误处理测试失败: 应该抛出异常")
                return False
            except Exception as e:
                print(f"✅ 错误处理测试通过: {type(e).__name__}")
                
            # 测试恢复
            safe_bag = bag.filter(lambda x: x != 25).map(lambda x: x * 2)
            result3 = safe_bag.sum().compute()
            print(f"✅ 恢复计算: {result3}")
            
            return True
            
    except Exception as e:
        print(f"❌ 错误恢复测试失败: {e}")
        return False


def main():
    print("🔧 Dask配置修复测试")
    print("=" * 50)
    
    # 测试配置
    test_dask_config()
    
    # 测试本地客户端
    if not test_local_client():
        print("❌ 本地客户端测试失败")
        return
    
    # 测试JSONL处理
    if not test_jsonl_processing():
        print("❌ JSONL处理测试失败")
        return
        
    # 测试错误恢复
    if not test_error_recovery():
        print("❌ 错误恢复测试失败")
        return
    
    print("\n🎉 所有测试通过!")
    print("✅ Dask配置修复成功")
    print("💡 现在可以安全使用 sampler_dask.py")
    
    # 提供使用建议
    print("\n💡 使用建议:")
    print("1. 小数据集测试:")
    print("   python sampler_dask.py --input 'test_data.jsonl' --output test_output --ratio 0.1 --n-workers 2")
    print("")
    print("2. 中等数据集:")
    print("   python sampler_dask.py --input '*.jsonl' --output sampled_data --ratio 0.1 --n-workers 4")
    print("")
    print("3. 监控Dashboard:")
    print("   http://localhost:8787")


if __name__ == "__main__":
    main()
