import time
import requests
import json
import statistics
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse


class APISpeedTester:
    def __init__(self, base_url: str, headers: Dict[str, str], payload_template: Dict[str, Any]):
        self.base_url = base_url
        self.headers = headers
        self.payload_template = payload_template
        self.results = []

    def single_request(self, prompt: str) -> Dict[str, Any]:
        """执行单个API请求并记录时间"""
        payload = self.payload_template.copy()

        # 根据不同的API格式调整payload
        if 'messages' in payload:
            # OpenAI/Claude格式
            payload['messages'] = [{"role": "user", "content": prompt}]
        elif 'prompt' in payload:
            # 其他格式
            payload['prompt'] = prompt

        start_time = time.time()

        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=payload,
                timeout=60
            )

            end_time = time.time()
            response_time = end_time - start_time

            if response.status_code == 200:
                response_data = response.json()

                # 提取响应内容和token信息
                content = self.extract_content(response_data)
                tokens = self.extract_token_info(response_data)

                return {
                    'success': True,
                    'response_time': response_time,
                    'status_code': response.status_code,
                    'content_length': len(content) if content else 0,
                    'tokens': tokens,
                    'prompt': prompt
                }
            else:
                return {
                    'success': False,
                    'response_time': response_time,
                    'status_code': response.status_code,
                    'error': response.text,
                    'prompt': prompt
                }

        except Exception as e:
            end_time = time.time()
            return {
                'success': False,
                'response_time': end_time - start_time,
                'error': str(e),
                'prompt': prompt
            }

    def extract_content(self, response_data: Dict) -> str:
        """从响应中提取内容"""
        # OpenAI格式
        if 'choices' in response_data:
            if response_data['choices']:
                choice = response_data['choices'][0]
                if 'message' in choice:
                    return choice['message'].get('content', '')
                elif 'text' in choice:
                    return choice['text']

        # Claude格式
        if 'content' in response_data:
            if isinstance(response_data['content'], list):
                return response_data['content'][0].get('text', '')
            return response_data['content']

        # 其他格式
        if 'response' in response_data:
            return response_data['response']

        return ''

    def extract_token_info(self, response_data: Dict) -> Dict[str, int]:
        """提取token使用信息"""
        tokens = {}

        if 'usage' in response_data:
            usage = response_data['usage']
            tokens = {
                'prompt_tokens': usage.get('prompt_tokens', 0),
                'completion_tokens': usage.get('completion_tokens', 0),
                'total_tokens': usage.get('total_tokens', 0)
            }

        return tokens

    def batch_test(self, prompts: List[str], concurrent: int = 1) -> List[Dict[str, Any]]:
        """批量测试API"""
        results = []

        if concurrent == 1:
            # 顺序执行
            for prompt in prompts:
                print(f"正在测试提示词: {prompt[:50]}...")
                result = self.single_request(prompt)
                results.append(result)
                time.sleep(0.1)  # 避免请求过于频繁
        else:
            # 并发执行
            with ThreadPoolExecutor(max_workers=concurrent) as executor:
                future_to_prompt = {
                    executor.submit(self.single_request, prompt): prompt
                    for prompt in prompts
                }

                for future in as_completed(future_to_prompt):
                    result = future.result()
                    results.append(result)
                    print(f"完成测试: {result['prompt'][:50]}...")

        return results

    def analyze_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析测试结果"""
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        if not successful_results:
            return {
                'total_requests': len(results),
                'successful_requests': 0,
                'failed_requests': len(failed_results),
                'success_rate': 0.0,
                'errors': [r.get('error', 'Unknown error') for r in failed_results]
            }

        response_times = [r['response_time'] for r in successful_results]

        # 计算tokens/秒（如果有token信息）
        tokens_per_second = []
        for r in successful_results:
            if r.get('tokens', {}).get('completion_tokens', 0) > 0:
                tps = r['tokens']['completion_tokens'] / r['response_time']
                tokens_per_second.append(tps)

        analysis = {
            'total_requests': len(results),
            'successful_requests': len(successful_results),
            'failed_requests': len(failed_results),
            'success_rate': len(successful_results) / len(results) * 100,
            'response_time': {
                'min': min(response_times),
                'max': max(response_times),
                'avg': statistics.mean(response_times),
                'median': statistics.median(response_times),
                'std': statistics.stdev(response_times) if len(response_times) > 1 else 0
            }
        }

        if tokens_per_second:
            analysis['tokens_per_second'] = {
                'min': min(tokens_per_second),
                'max': max(tokens_per_second),
                'avg': statistics.mean(tokens_per_second),
                'median': statistics.median(tokens_per_second)
            }

        # 错误统计
        if failed_results:
            error_counts = {}
            for r in failed_results:
                error = r.get('error', 'Unknown error')
                error_counts[error] = error_counts.get(error, 0) + 1
            analysis['errors'] = error_counts

        return analysis


def get_test_prompts() -> List[str]:
    """获取测试用的提示词"""
    return [
        "Hello, how are you?",
        "什么是人工智能？请简单解释。",
        "Write a short story about a robot.",
        "Explain quantum computing in simple terms.",
        "翻译这句话：The quick brown fox jumps over the lazy dog.",
        "编写一个Python函数来计算斐波那契数列。",
        "What are the benefits of renewable energy?",
        "解释一下机器学习和深度学习的区别。",
        "Create a haiku about autumn.",
        "如何做一道简单的中式炒饭？"
    ]


def print_results(analysis: Dict[str, Any]):
    """打印测试结果"""
    print("\n" + "=" * 50)
    print("API推理速度测试结果")
    print("=" * 50)

    print(f"总请求数: {analysis['total_requests']}")
    print(f"成功请求数: {analysis['successful_requests']}")
    print(f"失败请求数: {analysis['failed_requests']}")
    print(f"成功率: {analysis['success_rate']:.2f}%")

    if 'response_time' in analysis:
        rt = analysis['response_time']
        print(f"\n响应时间统计 (秒):")
        print(f"  最小值: {rt['min']:.3f}")
        print(f"  最大值: {rt['max']:.3f}")
        print(f"  平均值: {rt['avg']:.3f}")
        print(f"  中位数: {rt['median']:.3f}")
        print(f"  标准差: {rt['std']:.3f}")

    if 'tokens_per_second' in analysis:
        tps = analysis['tokens_per_second']
        print(f"\nTokens/秒统计:")
        print(f"  最小值: {tps['min']:.2f}")
        print(f"  最大值: {tps['max']:.2f}")
        print(f"  平均值: {tps['avg']:.2f}")
        print(f"  中位数: {tps['median']:.2f}")

    if 'errors' in analysis:
        print(f"\n错误统计:")
        for error, count in analysis['errors'].items():
            print(f"  {error}: {count}次")


def main():
    parser = argparse.ArgumentParser(description='AI模型API推理速度测试')
    parser.add_argument('--url', required=True, help='API端点URL')
    parser.add_argument('--api-key', help='API密钥')
    parser.add_argument('--concurrent', type=int, default=1, help='并发请求数')
    parser.add_argument('--model', default='gpt-3.5-turbo', help='模型名称')
    parser.add_argument('--requests', type=int, default=10, help='总请求数量')

    args = parser.parse_args()

    # 配置请求头和payload模板
    headers = {
        'Content-Type': 'application/json'
    }

    if args.api_key:
        headers['Authorization'] = f'Bearer {args.api_key}'

    # OpenAI格式的payload模板
    payload_template = {
        'model': args.model,
        'messages': [],
        'max_tokens': 150,
        'temperature': 0.7
    }

    # 创建测试器
    tester = APISpeedTester(args.url, headers, payload_template)

    # 获取测试提示词
    all_prompts = get_test_prompts()
    test_prompts = all_prompts[:args.requests]

    print(f"开始测试API: {args.url}")
    print(f"模型: {args.model}")
    print(f"并发数: {args.concurrent}")
    print(f"请求数量: {len(test_prompts)}")

    # 执行测试
    start_time = time.time()
    results = tester.batch_test(test_prompts, args.concurrent)
    total_time = time.time() - start_time

    # 分析结果
    analysis = tester.analyze_results(results)

    print(f"\n总测试时间: {total_time:.3f}秒")
    print(f"平均QPS: {len(test_prompts) / total_time:.2f}")

    # 打印结果
    print_results(analysis)


if __name__ == "__main__":
    # 如果没有命令行参数，使用示例配置
    import sys

    if len(sys.argv) == 1:
        print("使用示例:")
        print(
            "python api_speed_test.py --url https://api.openai.com/v1/chat/completions --api-key your_key --model gpt-3.5-turbo --concurrent 3 --requests 10")
        print("\n或者直接修改下面的配置进行测试:")

        # 示例配置 - 请根据实际情况修改
        API_URL = "https://api.openai.com/v1/chat/completions"
        API_KEY = "your_api_key_here"  # 替换为你的API密钥
        MODEL = "gpt-3.5-turbo"

        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {API_KEY}'
        }

        payload_template = {
            'model': MODEL,
            'messages': [],
            'max_tokens': 150,
            'temperature': 0.7
        }

        tester = APISpeedTester(API_URL, headers, payload_template)
        test_prompts = get_test_prompts()[:5]  # 测试前5个提示词

        print("开始示例测试...")
        results = tester.batch_test(test_prompts, concurrent=1)
        analysis = tester.analyze_results(results)
        print_results(analysis)
    else:
        main()