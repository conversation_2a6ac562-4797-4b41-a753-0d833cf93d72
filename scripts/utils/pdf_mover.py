import json
import os
import shutil
from pathlib import Path
from concurrent.futures import Thr<PERSON>PoolExecutor, ProcessPoolExecutor
import time


def build_file_index(source_dir):
    """预先构建文件索引，避免重复遍历"""
    print("正在构建文件索引...")
    start_time = time.time()

    file_index = {}

    # 使用os.scandir替代os.walk，更快
    def scan_directory(directory):
        local_index = {}
        try:
            with os.scandir(directory) as entries:
                for entry in entries:
                    if entry.is_file():
                        local_index[entry.name] = entry.path
                    elif entry.is_dir():
                        # 递归扫描子目录
                        sub_index = scan_directory(entry.path)
                        local_index.update(sub_index)
        except PermissionError:
            print(f"权限错误，跳过目录: {directory}")
        return local_index

    file_index = scan_directory(source_dir)

    elapsed = time.time() - start_time
    print(f"文件索引构建完成，共 {len(file_index)} 个文件，耗时 {elapsed:.2f} 秒")

    return file_index


def move_single_file(args):
    """移动单个文件的函数，用于并行处理"""
    item, file_index, target_dir = args
    filename = item["filename"]
    is_chinese = item["primary_language"] == "cmn"

    # 从索引中查找文件
    if filename not in file_index:
        return f"未找到: {filename}", False

    source_path = file_index[filename]
    target_subdir = "中文" if is_chinese else "非中文"
    target_path = Path(target_dir) / target_subdir / filename

    try:
        # 检查目标文件是否已存在
        if target_path.exists():
            return f"目标已存在，跳过: {filename}", False

        shutil.move(source_path, str(target_path))
        return f"移动: {filename} -> {target_subdir}", True
    except Exception as e:
        return f"移动失败 {filename}: {str(e)}", False


def move_files_optimized():
    # 配置路径
    json_file = "./440000.json"
    source_dir = "/home/<USER>/datasets/联通项目通信专利"
    target_dir = "/home/<USER>/datasets/联通项目通信专利已处理"

    # 读取JSON
    print("读取JSON文件...")
    with open(json_file, 'r', encoding='utf-8') as f:
        files = json.load(f)
    print(f"需要处理 {len(files)} 个文件")

    # 创建目标目录
    Path(target_dir, "中文").mkdir(parents=True, exist_ok=True)
    Path(target_dir, "非中文").mkdir(parents=True, exist_ok=True)

    # 构建文件索引
    file_index = build_file_index(source_dir)

    # 准备并行处理的参数
    args_list = [(item, file_index, target_dir) for item in files]

    # 使用线程池进行并行处理
    print("开始移动文件...")
    start_time = time.time()
    moved = 0

    # 根据你的系统调整线程数，通常CPU核心数的2-4倍
    max_workers = min(32, len(files))  # 最多32个线程

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 使用map处理所有文件
        results = executor.map(move_single_file, args_list)

        # 处理结果
        for result, success in results:
            print(result)
            if success:
                moved += 1

            # 每1000个文件显示一次进度
            if moved % 1000 == 0:
                elapsed = time.time() - start_time
                print(f"已处理 {moved} 个文件，耗时 {elapsed:.2f} 秒")

    elapsed = time.time() - start_time
    print(f"完成! 总共移动了 {moved} 个文件，总耗时 {elapsed:.2f} 秒")


def move_files_batch_optimized():
    """另一种优化方案：批量处理"""
    json_file = "./440000.json"
    source_dir = "/home/<USER>/datasets/联通项目通信专利"
    target_dir = "/home/<USER>/datasets/联通项目通信专利已处理"

    # 读取JSON
    with open(json_file, 'r', encoding='utf-8') as f:
        files = json.load(f)

    # 创建目标目录
    Path(target_dir, "中文").mkdir(parents=True, exist_ok=True)
    Path(target_dir, "非中文").mkdir(parents=True, exist_ok=True)

    # 将文件按语言分组
    chinese_files = [item["filename"] for item in files if item["primary_language"] == "cmn"]
    non_chinese_files = [item["filename"] for item in files if item["primary_language"] != "cmn"]

    print(f"中文文件: {len(chinese_files)} 个")
    print(f"非中文文件: {len(non_chinese_files)} 个")

    moved = 0

    def move_batch(filenames, target_subdir):
        nonlocal moved
        target_full_path = Path(target_dir) / target_subdir

        for filename in filenames:
            # 直接尝试在几个可能的位置查找文件
            possible_paths = [
                Path(source_dir) / filename,
                # 如果有子目录结构，可以添加更多可能的路径
            ]

            found = None
            for path in possible_paths:
                if path.exists():
                    found = path
                    break

            if found:
                target_path = target_full_path / filename
                if not target_path.exists():
                    try:
                        shutil.move(str(found), str(target_path))
                        moved += 1
                        if moved % 1000 == 0:
                            print(f"已移动 {moved} 个文件...")
                    except Exception as e:
                        print(f"移动失败 {filename}: {e}")
            else:
                print(f"未找到: {filename}")

    print("开始移动中文文件...")
    move_batch(chinese_files, "中文")

    print("开始移动非中文文件...")
    move_batch(non_chinese_files, "非中文")

    print(f"完成! 移动了 {moved} 个文件")


if __name__ == "__main__":
    # 选择优化方案
    print("选择运行方案:")
    print("1. 索引+并行处理 (推荐)")
    print("2. 批量处理 (适合文件都在根目录)")

    choice = input("请输入选择 (1 或 2): ").strip()

    if choice == "2":
        move_files_batch_optimized()
    else:
        move_files_optimized()