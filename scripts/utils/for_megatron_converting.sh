#!/bin/bash

# 本脚本用于将指定目录下的所有 jsonl 文件按照大小进行分批处理，分别放到 batch1,batch2,batch3等目录下

# 检查参数
if [ $# -ne 1 ]; then
    echo "用法: $0 <源目录路径>"
    echo "示例: $0 /path/to/jsonl/files"
    exit 1
fi

SOURCE_DIR="$1"
BATCH_SIZE_GB=20
BATCH_SIZE_BYTES=$((BATCH_SIZE_GB * 1024 * 1024 * 1024))  # 20GB in bytes
BATCH_NUM=1
CURRENT_BATCH_SIZE=0

# 检查源目录是否存在
if [ ! -d "$SOURCE_DIR" ]; then
    echo "错误: 源目录 '$SOURCE_DIR' 不存在"
    exit 1
fi

# 检查源目录下是否有 jsonl 文件
if ! ls "$SOURCE_DIR"/*.jsonl >/dev/null 2>&1; then
    echo "错误: 源目录 '$SOURCE_DIR' 下没有找到 .jsonl 文件"
    exit 1
fi

echo "开始处理 $SOURCE_DIR 下的 jsonl 文件..."
echo "每批次大小限制: ${BATCH_SIZE_GB}GB"
echo "=========================="

# 创建第一个批次目录
CURRENT_BATCH_DIR="batch${BATCH_NUM}"
mkdir -p "$CURRENT_BATCH_DIR"
echo "创建目录: $CURRENT_BATCH_DIR"

# 遍历所有 jsonl 文件
for file in "$SOURCE_DIR"/*.jsonl; do
    # 检查文件是否存在（处理通配符没有匹配的情况）
    if [ ! -f "$file" ]; then
        continue
    fi

    # 获取文件大小（以字节为单位）
    file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)

    if [ -z "$file_size" ]; then
        echo "警告: 无法获取文件 $file 的大小，跳过"
        continue
    fi

    # 检查单个文件是否超过批次大小限制
    if [ "$file_size" -gt "$BATCH_SIZE_BYTES" ]; then
        echo "警告: 文件 $(basename "$file") 大小 $(($file_size / 1024 / 1024 / 1024))GB 超过批次限制，单独放入 batch${BATCH_NUM}"
        cp "$file" "$CURRENT_BATCH_DIR/"
        echo "  拷贝: $(basename "$file") -> $CURRENT_BATCH_DIR/ ($(($file_size / 1024 / 1024))MB)"

        # 创建下一个批次目录
        BATCH_NUM=$((BATCH_NUM + 1))
        CURRENT_BATCH_DIR="batch${BATCH_NUM}"
        mkdir -p "$CURRENT_BATCH_DIR"
        echo "创建目录: $CURRENT_BATCH_DIR"
        CURRENT_BATCH_SIZE=0
        continue
    fi

    # 检查是否需要创建新的批次目录
    if [ $((CURRENT_BATCH_SIZE + file_size)) -gt "$BATCH_SIZE_BYTES" ]; then
        echo "当前批次 batch${BATCH_NUM} 大小: $(($CURRENT_BATCH_SIZE / 1024 / 1024))MB"

        # 创建新的批次目录
        BATCH_NUM=$((BATCH_NUM + 1))
        CURRENT_BATCH_DIR="batch${BATCH_NUM}"
        mkdir -p "$CURRENT_BATCH_DIR"
        echo "创建目录: $CURRENT_BATCH_DIR"
        CURRENT_BATCH_SIZE=0
    fi

    # 拷贝文件到当前批次目录
    cp "$file" "$CURRENT_BATCH_DIR/"
    CURRENT_BATCH_SIZE=$((CURRENT_BATCH_SIZE + file_size))

    echo "  拷贝: $(basename "$file") -> $CURRENT_BATCH_DIR/ ($(($file_size / 1024 / 1024))MB)"
done

# 显示最后一个批次的大小
if [ "$CURRENT_BATCH_SIZE" -gt 0 ]; then
    echo "当前批次 batch${BATCH_NUM} 大小: $(($CURRENT_BATCH_SIZE / 1024 / 1024))MB"
fi

echo "=========================="
echo "处理完成！共创建了 $BATCH_NUM 个批次目录"

# 显示每个批次的统计信息
echo -e "\n批次统计:"
for i in $(seq 1 $BATCH_NUM); do
    batch_dir="batch${i}"
    if [ -d "$batch_dir" ]; then
        file_count=$(ls -1 "$batch_dir"/*.jsonl 2>/dev/null | wc -l)
        batch_size=$(du -sh "$batch_dir" 2>/dev/null | cut -f1)
        echo "  $batch_dir: $file_count 个文件, 总大小: $batch_size"
    fi
done