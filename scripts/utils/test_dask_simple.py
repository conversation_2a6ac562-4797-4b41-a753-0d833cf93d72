#!/usr/bin/env python3
"""
🔧 简化的Dask测试脚本
验证修复后的Dask配置
"""

import sys
import json
import tempfile
import os

try:
    import dask
    import dask.bag as db
    from dask.distributed import Client
    print(f"✅ Dask导入成功: {dask.__version__}")
except ImportError as e:
    print(f"❌ Dask未安装: {e}")
    sys.exit(1)


def test_basic_dask():
    """基础Dask测试"""
    print("🧪 基础Dask测试...")
    
    # 设置简化配置
    dask.config.set({
        'distributed.worker.daemon': False,
        'distributed.comm.timeouts.connect': '30s',
        'distributed.worker.memory.target': 0.6,
        'array.chunk-size': '128MB',
    })
    
    try:
        # 使用最简单的配置
        with Client(
            processes=False,  # 使用线程模式
            threads_per_worker=2,
            n_workers=2,
            memory_limit='2GB',
            silence_logs=True,  # 减少日志输出
            dashboard_address=None  # 禁用dashboard避免端口冲突
        ) as client:
            print("✅ Dask客户端创建成功")
            
            # 简单计算测试
            data = list(range(100))
            bag = db.from_sequence(data, partition_size=20)
            result = bag.map(lambda x: x * 2).sum().compute()
            expected = sum(x * 2 for x in data)
            
            if result == expected:
                print(f"✅ 计算测试通过: {result}")
                return True
            else:
                print(f"❌ 计算测试失败: {result} != {expected}")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_jsonl_simple():
    """简化的JSONL测试"""
    print("📄 JSONL处理测试...")
    
    # 创建测试数据
    test_data = [
        {'id': 'test_1', 'source': 'A', 'value': 1},
        {'id': 'test_2', 'source': 'B', 'value': 2},
        {'id': 'test_3', 'source': 'A', 'value': 3},
        {'id': 'test_4', 'source': 'C', 'value': 4},
        {'id': 'test_5', 'source': 'B', 'value': 5},
    ]
    
    # 写入临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
        for item in test_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
        temp_file = f.name
    
    try:
        with Client(processes=False, threads_per_worker=1, n_workers=1, silence_logs=True, dashboard_address=None) as client:
            # 读取文件
            bag = db.read_text(temp_file)
            
            # 解析JSON
            def parse_json(line):
                try:
                    return json.loads(line.strip())
                except:
                    return None
            
            parsed_bag = bag.map(parse_json).filter(lambda x: x is not None)
            
            # 统计
            count = parsed_bag.count().compute()
            sources = parsed_bag.pluck('source').frequencies().compute()
            
            print(f"✅ 处理了 {count} 条记录")
            print(f"✅ Source分布: {dict(sources)}")
            
            return count == len(test_data)
            
    except Exception as e:
        print(f"❌ JSONL测试失败: {e}")
        return False
    finally:
        try:
            os.unlink(temp_file)
        except:
            pass


def main():
    print("🔧 Dask简化测试")
    print("=" * 30)
    
    # 基础测试
    if not test_basic_dask():
        print("❌ 基础测试失败")
        return
    
    # JSONL测试
    if not test_jsonl_simple():
        print("❌ JSONL测试失败")
        return
    
    print("\n🎉 所有测试通过!")
    print("✅ Dask配置修复成功")
    print("💡 可以使用 sampler_dask.py 了")


if __name__ == "__main__":
    main()
