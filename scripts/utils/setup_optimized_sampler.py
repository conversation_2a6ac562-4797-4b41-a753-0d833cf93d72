#!/usr/bin/env python3
"""
优化版抽样工具安装和配置脚本
自动安装依赖并提供使用指南
"""

import os
import sys
import subprocess
import importlib
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version}")
    return True


def install_package(package_name, import_name=None):
    """安装Python包"""
    if import_name is None:
        import_name = package_name
        
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"📦 正在安装 {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ {package_name} 安装失败")
            return False


def install_dependencies():
    """安装所有依赖"""
    print("🔧 检查和安装依赖包...")
    
    dependencies = [
        ("orjson", "orjson"),  # 高性能JSON库
        ("psutil", "psutil"),  # 系统监控
        ("numpy", "numpy"),    # 数值计算（可选，用于更快的随机采样）
    ]
    
    success_count = 0
    for package, import_name in dependencies:
        if install_package(package, import_name):
            success_count += 1
    
    print(f"\n📊 依赖安装结果: {success_count}/{len(dependencies)} 成功")
    
    if success_count == len(dependencies):
        print("✅ 所有依赖安装完成")
        return True
    else:
        print("⚠️ 部分依赖安装失败，但脚本仍可运行（性能可能受影响）")
        return False


def create_example_config():
    """创建示例配置文件"""
    config_content = """# 优化版抽样工具配置示例

# 基本配置
input_pattern: "*.jsonl"          # 输入文件模式
output_prefix: "sampled_data"     # 输出文件前缀
sample_ratio: 0.1                 # 抽样比例 (10%)
random_seed: 42                   # 随机种子

# 性能配置
max_workers: null                 # 最大线程数 (null=自动)
max_lines_per_file: 10000        # 每个输出文件最大行数
batch_size: 50000                 # 批处理大小

# 高级配置
enable_profiling: false           # 启用性能分析
memory_limit_mb: 8192            # 内存限制 (MB)
chunk_size_kb: 64                # 文件读取块大小 (KB)
"""
    
    config_file = Path("sampler_config.yaml")
    if not config_file.exists():
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        print(f"✅ 创建示例配置文件: {config_file}")
    else:
        print(f"ℹ️ 配置文件已存在: {config_file}")


def print_usage_guide():
    """打印使用指南"""
    print("\n" + "=" * 80)
    print("📚 优化版抽样工具使用指南")
    print("=" * 80)
    
    print("\n🚀 基本用法:")
    print("python sampler_optimized.py --input '*.jsonl' --output sampled_data --ratio 0.1")
    
    print("\n📋 主要参数:")
    print("  --input, -i      输入文件模式 (默认: *.jsonl)")
    print("  --output, -o     输出文件前缀 (默认: sampled_data)")
    print("  --ratio, -r      抽样比例 (默认: 0.1 即10%)")
    print("  --seed, -s       随机种子 (默认: 42)")
    print("  --max-workers    最大线程数 (默认: 自动)")
    print("  --max-lines      每个输出文件最大行数 (默认: 10,000)")
    print("  --profile        启用详细性能分析")
    
    print("\n💡 使用示例:")
    print("# 1. 基本抽样 (10%)")
    print("python sampler_optimized.py")
    
    print("\n# 2. 指定输入和输出")
    print("python sampler_optimized.py -i 'data/*.jsonl' -o output/sample")
    
    print("\n# 3. 高比例抽样 (50%)")
    print("python sampler_optimized.py --ratio 0.5")
    
    print("\n# 4. 性能优化 (使用更多线程)")
    print("python sampler_optimized.py --max-workers 16")
    
    print("\n# 5. 启用性能分析")
    print("python sampler_optimized.py --profile")
    
    print("\n# 6. 处理历史记录")
    print("python sampler_optimized.py --previous-records record1.json record2.json")
    
    print("\n🔧 性能对比测试:")
    print("python benchmark_sampler.py --test-files 5 --lines-per-file 20000")
    
    print("\n📊 性能优化建议:")
    print("1. 安装orjson库可提升JSON解析速度 2-5倍")
    print("2. 使用SSD存储可显著提升I/O性能")
    print("3. 增加系统内存可减少磁盘交换")
    print("4. 根据CPU核心数调整--max-workers参数")
    print("5. 对于超大文件，考虑分批处理")
    
    print("\n⚠️ 注意事项:")
    print("1. 确保有足够的磁盘空间存储输出文件")
    print("2. 大数据集处理时监控内存使用情况")
    print("3. 使用相同的随机种子可确保结果可重复")
    print("4. 历史记录文件用于避免重复采样")


def check_system_resources():
    """检查系统资源"""
    try:
        import psutil
        
        print("\n🖥️ 系统资源检查:")
        
        # CPU信息
        cpu_count = os.cpu_count()
        print(f"  CPU核心数: {cpu_count}")
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        print(f"  总内存: {memory_gb:.1f}GB")
        print(f"  可用内存: {memory.available / (1024**3):.1f}GB")
        
        # 磁盘信息
        disk = psutil.disk_usage('.')
        disk_free_gb = disk.free / (1024**3)
        print(f"  可用磁盘空间: {disk_free_gb:.1f}GB")
        
        # 性能建议
        print("\n💡 性能建议:")
        if cpu_count >= 8:
            print(f"  ✅ CPU核心充足，建议使用 --max-workers {cpu_count * 2}")
        else:
            print(f"  ⚠️ CPU核心较少，建议使用 --max-workers {cpu_count}")
            
        if memory_gb >= 16:
            print("  ✅ 内存充足，可处理大型数据集")
        elif memory_gb >= 8:
            print("  ⚠️ 内存中等，建议分批处理超大数据集")
        else:
            print("  ❌ 内存较少，建议增加内存或使用小批次处理")
            
        if disk_free_gb >= 50:
            print("  ✅ 磁盘空间充足")
        else:
            print("  ⚠️ 磁盘空间不足，请清理空间或更换存储位置")
            
    except ImportError:
        print("⚠️ 无法检查系统资源 (psutil未安装)")


def main():
    print("🚀 优化版抽样工具安装和配置")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 安装依赖
    install_dependencies()
    
    # 检查系统资源
    check_system_resources()
    
    # 创建示例配置
    create_example_config()
    
    # 打印使用指南
    print_usage_guide()
    
    print("\n" + "=" * 80)
    print("🎉 安装和配置完成!")
    print("现在可以使用 python sampler_optimized.py 开始高速抽样")
    print("=" * 80)


if __name__ == "__main__":
    main()
