#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Flexible S3 File Download Script (Python 2.7 compatible)
Supports configuration files, command line arguments, batch downloads, and more
"""

import boto
import boto.s3.connection
import os
import sys
import argparse
import json
import logging
import fnmatch
import threading
import time
from Queue import Queue
from threading import Thread


class S3Downloader(object):
    def __init__(self, config):
        """
        Initialize S3 downloader

        Args:
            config: Configuration dictionary containing connection info and download parameters
        """
        self.config = config
        self.setup_logging()
        self.setup_connection()
        self.download_queue = Queue()
        self.results = {'success': 0, 'failed': 0, 'skipped': 0, 'files': []}
        self.lock = threading.Lock()

    def setup_logging(self):
        """Setup logging"""
        log_level = getattr(logging, self.config.get('log_level', 'INFO').upper())

        # Create logger
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(log_level)

        # Clear existing handlers to avoid duplicate logs
        if self.logger.handlers:
            self.logger.handlers.clear()

        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

        # File handler
        file_handler = logging.FileHandler('s3_download.log')
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)

        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)

        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    def setup_connection(self):
        """Establish S3 connection"""
        try:
            self.conn = boto.connect_s3(
                aws_access_key_id=self.config['access_key'],
                aws_secret_access_key=self.config['secret_key'],
                host=self.config['host'],
                is_secure=self.config.get('is_secure', True),
                calling_format=boto.s3.connection.OrdinaryCallingFormat(),
            )
            self.logger.info("S3 connection successful: %s" % self.config['host'])
        except Exception as e:
            self.logger.error("S3 connection failed: %s" % str(e))
            raise

    def list_buckets(self):
        """List all buckets"""
        try:
            buckets = [bucket.name for bucket in self.conn.get_all_buckets()]
            self.logger.info("Found %d buckets" % len(buckets))
            for bucket in buckets:
                self.logger.info("Bucket: %s" % bucket)
                print("%s\t%s" % (bucket, ""))
            return buckets
        except Exception as e:
            self.logger.error("List buckets failed: %s" % str(e))
            return []

    def list_files(self, bucket_name, prefix="", pattern="*"):
        """
        List files in bucket

        Args:
            bucket_name: Bucket name
            prefix: File prefix filter
            pattern: File name pattern matching (supports wildcards)

        Returns:
            List of file information
        """
        files = []
        try:
            bucket = self.conn.get_bucket(bucket_name)
            for key in bucket.list(prefix=prefix):
                if fnmatch.fnmatch(key.name, pattern):
                    files.append({
                        'name': key.name,
                        'size': key.size,
                        'modified': key.last_modified,
                        'key_obj': key  # Save key object for download
                    })

            self.logger.info("In bucket %s found %d matched files" % (bucket_name, len(files)))
            return files
        except Exception as e:
            self.logger.error("List files failed: %s" % str(e))
            return []

    def file_exists_and_same_size(self, local_path, remote_size):
        """
        Check if local file exists and has the same size as remote file

        Args:
            local_path: Local file path
            remote_size: Remote file size

        Returns:
            True if file exists and has same size, False otherwise
        """
        if not os.path.exists(local_path):
            return False

        local_size = os.path.getsize(local_path)
        return local_size == remote_size

    def download_file(self, bucket_name, key_name, local_path, key_obj=None, skip_existing=True):
        """
        Download single file

        Args:
            bucket_name: Bucket name
            key_name: File key name
            local_path: Local save path
            key_obj: Optional key object to avoid repeated retrieval
            skip_existing: Skip download if local file exists with same size

        Returns:
            Tuple of (success, skipped)
        """
        try:
            # Check if file already exists with same size
            if skip_existing and key_obj and self.file_exists_and_same_size(local_path, key_obj.size):
                self.logger.info("File already exists, skipping: %s -> %s (%d bytes)" %
                                 (key_name, local_path, key_obj.size))
                return True, True

            # Ensure directory exists
            local_dir = os.path.dirname(local_path)
            if not os.path.exists(local_dir):
                os.makedirs(local_dir)

            if key_obj is None:
                bucket = self.conn.get_bucket(bucket_name)
                key_obj = bucket.get_key(key_name)
                if key_obj is None:
                    self.logger.error("File does not exist: %s" % key_name)
                    return False, False

            key_obj.get_contents_to_filename(local_path)

            file_size = os.path.getsize(local_path)
            self.logger.info("Successfully downloaded: %s -> %s (%d bytes)" %
                             (key_name, local_path, file_size))
            return True, False
        except Exception as e:
            self.logger.error("Download file %s failed: %s" % (key_name, str(e)))
            return False, False

    def worker_thread(self, bucket_name, keep_structure, base_path, skip_existing):
        """Worker thread function"""
        while True:
            try:
                file_info = self.download_queue.get(timeout=1)
                if file_info is None:
                    break

                file_name = file_info['name']
                key_obj = file_info.get('key_obj')

                if keep_structure:
                    local_path = os.path.join(base_path, file_name)
                else:
                    local_path = os.path.join(base_path, os.path.basename(file_name))

                success, skipped = self.download_file(bucket_name, file_name, local_path,
                                                      key_obj, skip_existing)

                with self.lock:
                    if success:
                        if skipped:
                            self.results['skipped'] += 1
                        else:
                            self.results['success'] += 1
                            self.results['files'].append({'file': file_name, 'path': local_path})
                    else:
                        self.results['failed'] += 1

                self.download_queue.task_done()
            except:
                break

    def download_files(self, bucket_name, files, base_path, max_workers=4,
                       keep_structure=True, skip_existing=True):
        """
        Batch download files

        Args:
            bucket_name: Bucket name
            files: List of file information
            base_path: Local save base path
            max_workers: Maximum concurrent workers
            keep_structure: Whether to maintain directory structure
            skip_existing: Skip files that already exist locally with same size

        Returns:
            Download result statistics
        """
        # Reset results
        self.results = {'success': 0, 'failed': 0, 'skipped': 0, 'files': []}

        # Add files to queue
        for file_info in files:
            self.download_queue.put(file_info)

        # Create worker threads
        threads = []
        for i in range(max_workers):
            t = Thread(target=self.worker_thread,
                       args=(bucket_name, keep_structure, base_path, skip_existing))
            t.daemon = True
            t.start()
            threads.append(t)

        # Wait for all tasks to complete
        self.download_queue.join()

        # Stop all worker threads
        for i in range(max_workers):
            self.download_queue.put(None)

        for t in threads:
            t.join()

        self.logger.info("Download completed - Success: %d, Failed: %d, Skipped: %d" %
                         (self.results['success'], self.results['failed'], self.results['skipped']))
        return self.results


def load_config(config_file):
    """Load configuration from file"""
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            return json.load(f)
    return {}


def create_default_config():
    """Create default configuration file"""
    config = {
        "access_key": "your_access_key_here",
        "secret_key": "your_secret_key_here",
        "host": "your_s3_host_here",
        "is_secure": False,
        "log_level": "INFO",
        "default_bucket": "your-bucket-name",
        "download_path": "./downloads",
        "max_workers": 4,
        "keep_structure": True,
        "skip_existing": True
    }

    with open('s3_config.json', 'w') as f:
        json.dump(config, f, indent=2)

    print("Default configuration file 's3_config.json' created. Please modify the configuration information.")


def simple_download(access_key, secret_key, host, bucket_name, file_prefix, save_path,
                    is_secure=False, skip_existing=True):
    """
    Simplified download function, maintaining similar usage as original script
    """
    config = {
        'access_key': access_key,
        'secret_key': secret_key,
        'host': host,
        'is_secure': is_secure,
        'log_level': 'INFO'
    }

    downloader = S3Downloader(config)

    try:
        # List files
        files = downloader.list_files(bucket_name, prefix=file_prefix)
        if not files:
            print("No matching files found")
            return

        print("Found %d files:" % len(files))
        for file_info in files:
            print("%s\t%s\t%s" % (file_info['name'], file_info['size'], file_info['modified']))

        # Download files
        file_names_with_keys = [{'name': f['name'], 'key_obj': f['key_obj']} for f in files]
        results = downloader.download_files(bucket_name, file_names_with_keys, save_path,
                                            skip_existing=skip_existing)

        print("Download results - Success: %d, Failed: %d, Skipped: %d" %
              (results['success'], results['failed'], results['skipped']))

    except Exception as e:
        print("Download process error: %s" % str(e))


def main():
    parser = argparse.ArgumentParser(description='S3 File Download Tool')
    parser.add_argument('--config', default='s3_config.json', help='Configuration file path')
    parser.add_argument('--bucket', help='Bucket name')
    parser.add_argument('--prefix', default='', help='File prefix filter')
    parser.add_argument('--pattern', default='*', help='File name pattern matching')
    parser.add_argument('--output', help='Output directory')
    parser.add_argument('--list-buckets', action='store_true', help='List all buckets')
    parser.add_argument('--list-files', action='store_true', help='List files (no download)')
    parser.add_argument('--create-config', action='store_true', help='Create default configuration file')
    parser.add_argument('--max-workers', type=int, help='Maximum concurrent workers')
    parser.add_argument('--no-structure', action='store_true', help='Do not maintain directory structure')
    parser.add_argument('--force-download', action='store_true', help='Force download even if file exists')

    # Simple mode arguments
    parser.add_argument('--simple', action='store_true', help='Use simple mode')
    parser.add_argument('--access-key', help='Access Key')
    parser.add_argument('--secret-key', help='Secret Key')
    parser.add_argument('--host', help='S3 host address')
    parser.add_argument('--insecure', action='store_true', help='Do not use SSL')

    args = parser.parse_args()

    if args.create_config:
        create_default_config()
        return

    # Simple mode - similar to original script usage
    if args.simple:
        if not all([args.access_key, args.secret_key, args.host, args.bucket]):
            print("Simple mode requires: --access-key, --secret-key, --host, --bucket")
            return

        output_path = args.output or "/tmp/downloads"
        simple_download(
            args.access_key, args.secret_key, args.host,
            args.bucket, args.prefix, output_path,
            not args.insecure, not args.force_download
        )
        return

    # Configuration file mode
    config = load_config(args.config)
    if not config:
        print("Configuration file %s does not exist. Use --create-config to create default configuration" % args.config)
        return

    # Command line arguments override configuration file
    if args.bucket:
        config['bucket'] = args.bucket
    if args.output:
        config['download_path'] = args.output
    if args.max_workers:
        config['max_workers'] = args.max_workers
    if args.force_download:
        config['skip_existing'] = False

    # Initialize downloader
    downloader = S3Downloader(config)

    try:
        if args.list_buckets:
            downloader.list_buckets()
            return

        bucket_name = config.get('bucket') or config.get('default_bucket')
        if not bucket_name:
            print("Please specify bucket name (use --bucket parameter or set in configuration file)")
            return

        # List files
        files = downloader.list_files(bucket_name, args.prefix, args.pattern)
        if not files:
            print("No matching files found")
            return

        if args.list_files:
            print("Found %d files:" % len(files))
            for file_info in files:
                print("%s\t%s bytes\t%s" % (file_info['name'], file_info['size'], file_info['modified']))
            return

        # Download files
        base_path = config.get('download_path', './downloads')
        max_workers = config.get('max_workers', 4)
        keep_structure = not args.no_structure
        skip_existing = config.get('skip_existing', True) and not args.force_download

        print("Starting download of %d files to %s" % (len(files), base_path))
        if skip_existing:
            print("Existing files with same size will be skipped (use --force-download to override)")

        results = downloader.download_files(
            bucket_name, files, base_path, max_workers, keep_structure, skip_existing
        )

        print("Download results - Success: %d, Failed: %d, Skipped: %d" %
              (results['success'], results['failed'], results['skipped']))

    except KeyboardInterrupt:
        print("Download interrupted by user")
    except Exception as e:
        print("Program error: %s" % str(e))


# Compatible with original script usage
if __name__ == "__main__":
    # If run directly without command line arguments, show usage examples
    if len(sys.argv) == 1:
        print("Usage examples:")
        print("1. Create config file: python %s --create-config" % sys.argv[0])
        print("2. Use config file: python %s --bucket ai-milvus --prefix 'AI/pretrained_models/'" % sys.argv[0])
        print(
            "3. Simple mode: python %s --simple --access-key xxx --secret-key yyy --host ******* --bucket ai-milvus --prefix 'AI/pretrained_models/' --insecure" %
            sys.argv[0])
        print("4. Force download existing files: python %s --bucket ai-milvus --force-download" % sys.argv[0])

        # Demonstrate original script conversion
        print("\nConvert your original script to:")
        print("simple_download(")
        print("    access_key='xxxxxxxxxxxxx',")
        print("    secret_key='yyyyyyyyyyyyyyyyyyyyy',")
        print("    host='*******',")
        print("    bucket_name='ai-milvus',")
        print("    file_prefix='AI/pretrained_models/OLMo2-8B-SuperBPE-t180k.zip',")
        print("    save_path='/home/<USER>/zhangsj/',")
        print("    is_secure=False,")
        print("    skip_existing=True  # Skip files that already exist with same size")
        print(")")
    else:
        main()