#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import argparse
from pathlib import Path
from typing import Dict, List, Optional


def extract_filename_from_path(pdf_path: str) -> str:
    """
    从PDF路径中提取文件名（不含扩展名）
    """
    return Path(pdf_path).stem


def find_matching_files(base_dir: str, pdf_filename: str) -> Dict[str, Optional[str]]:
    """
    在指定目录下查找匹配的MD文件和chunk JSON文件

    Args:
        base_dir: 搜索的基础目录
        pdf_filename: PDF文件名（不含扩展名）

    Returns:
        包含md_path和chunk_path的字典
    """
    base_path = Path(base_dir)
    result = {"md_path": None, "chunk_path": None}

    # 搜索所有可能的子目录
    for root, dirs, files in os.walk(base_path):
        root_path = Path(root)

        # 查找MD文件
        for file in files:
            if file.endswith('.md') and pdf_filename in file:
                result["md_path"] = str((root_path / file).resolve())
                break

        # 查找chunk JSON文件
        for file in files:
            if file.endswith('_chunk.json') and pdf_filename in file:
                result["chunk_path"] = str((root_path / file).resolve())
                break

        # 如果两个文件都找到了，可以提前退出
        if result["md_path"] and result["chunk_path"]:
            break

    return result


def process_jsonl_file(input_file: str, output_file: str, search_dir: str) -> None:
    """
    处理输入的JSONL文件，查找匹配的文件并生成输出

    Args:
        input_file: 输入的JSONL文件路径
        output_file: 输出的JSONL文件路径
        search_dir: 搜索目录
    """
    matched_count = 0
    total_count = 0

    with open(input_file, 'r', encoding='utf-8') as infile, \
            open(output_file, 'w', encoding='utf-8') as outfile:

        for line in infile:
            line = line.strip()
            if not line:
                continue

            try:
                data = json.loads(line)
                total_count += 1

                pdf_path = data.get('raw_content', '')
                if not pdf_path:
                    print(f"警告: 第{total_count}行没有raw_content字段")
                    continue

                # 提取PDF文件名
                pdf_filename = extract_filename_from_path(pdf_path)
                print(f"处理文件: {pdf_filename}")

                # 查找匹配的文件
                matched_files = find_matching_files(search_dir, pdf_filename)

                # 如果找到了MD文件或chunk文件，则生成输出
                if matched_files["md_path"] or matched_files["chunk_path"]:
                    output_data = {
                        "raw_content": pdf_path
                    }

                    # 只有找到文件时才添加对应字段
                    if matched_files["md_path"]:
                        output_data["text_path"] = matched_files["md_path"]
                    if matched_files["chunk_path"]:
                        output_data["chunk_path"] = matched_files["chunk_path"]

                    # 写入输出文件
                    json.dump(output_data, outfile, ensure_ascii=False)
                    outfile.write('\n')
                    matched_count += 1

                    print(f"  ✓ 找到匹配文件:")
                    if matched_files["md_path"]:
                        print(f"    MD: {matched_files['md_path']}")
                    if matched_files["chunk_path"]:
                        print(f"    Chunk: {matched_files['chunk_path']}")
                else:
                    print(f"  ✗ 未找到匹配文件")

            except json.JSONDecodeError as e:
                print(f"错误: 第{total_count}行JSON解析失败: {e}")
                continue
            except Exception as e:
                print(f"错误: 处理第{total_count}行时发生异常: {e}")
                continue

    print(f"\n处理完成:")
    print(f"总共处理: {total_count} 个文件")
    print(f"找到匹配: {matched_count} 个文件")
    print(f"输出文件: {output_file}")


def main():
    parser = argparse.ArgumentParser(description='处理PDF文件路径并查找对应的MD和chunk JSON文件')
    parser.add_argument('input_file', help='输入的JSONL文件路径')
    parser.add_argument('-o', '--output', default='output.jsonl', help='输出的JSONL文件路径 (默认: output.jsonl)')
    parser.add_argument('-d', '--search-dir',
                        default='/home/<USER>/open-dataflow/example_data/KBCleaningPipeline/part_aa/',
                        help='搜索目录 (默认: /home/<USER>/open-dataflow/example_data/KBCleaningPipeline/part_aa/)')

    args = parser.parse_args()

    # 检查输入文件是否存在
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件不存在: {args.input_file}")
        return

    # 检查搜索目录是否存在
    if not os.path.exists(args.search_dir):
        print(f"错误: 搜索目录不存在: {args.search_dir}")
        return

    print(f"输入文件: {args.input_file}")
    print(f"搜索目录: {args.search_dir}")
    print(f"输出文件: {args.output}")
    print("-" * 50)

    # 处理文件
    process_jsonl_file(args.input_file, args.output, args.search_dir)


if __name__ == "__main__":
    main()
