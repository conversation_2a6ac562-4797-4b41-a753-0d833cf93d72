#!/usr/bin/env python3
"""
🚀 基于Spark的分布式JSONL文件抽样工具
支持TB级数据处理，具备容错和弹性扩展能力

特性：
1. 分布式处理 - 支持多机集群
2. 内存计算 - 高性能数据处理
3. 智能分区 - 优化数据分布
4. 容错机制 - 自动故障恢复
5. 动态扩展 - 弹性资源管理
"""

import os
import sys
import time
import argparse
import json
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from pathlib import Path

try:
    from pyspark.sql import SparkSession, DataFrame
    from pyspark.sql.functions import (
        col, rand, count, collect_list, 
        monotonically_increasing_id, row_number, 
        when, isnan, isnull, size, explode
    )
    from pyspark.sql.window import Window
    from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType
    from pyspark import SparkContext, SparkConf
    SPARK_AVAILABLE = True
except ImportError:
    SPARK_AVAILABLE = False
    print("❌ PySpark未安装，请运行: pip install pyspark")
    sys.exit(1)


class SparkSamplerConfig:
    """Spark采样器配置"""
    
    def __init__(self):
        # Spark基础配置
        self.app_name = "DistributedJSONLSampler"
        self.master = "local[*]"  # 默认本地模式
        
        # 性能优化配置
        self.executor_memory = "4g"
        self.driver_memory = "2g"
        self.executor_cores = "2"
        self.max_result_size = "2g"
        
        # 分区和缓存配置
        self.default_parallelism = None  # 自动检测
        self.sql_adaptive_enabled = True
        self.sql_adaptive_coalesce_partitions_enabled = True
        
        # 序列化配置
        self.serializer = "org.apache.spark.serializer.KryoSerializer"
        self.kryo_unsafe = True
        
        # 动态分配配置
        self.dynamic_allocation_enabled = True
        self.dynamic_allocation_min_executors = 1
        self.dynamic_allocation_max_executors = 10
        
    def to_spark_conf(self) -> SparkConf:
        """转换为Spark配置"""
        conf = SparkConf()
        conf.setAppName(self.app_name)
        conf.setMaster(self.master)
        
        # 内存配置
        conf.set("spark.executor.memory", self.executor_memory)
        conf.set("spark.driver.memory", self.driver_memory)
        conf.set("spark.executor.cores", self.executor_cores)
        conf.set("spark.driver.maxResultSize", self.max_result_size)
        
        # 性能优化
        if self.default_parallelism:
            conf.set("spark.default.parallelism", str(self.default_parallelism))
        conf.set("spark.sql.adaptive.enabled", str(self.sql_adaptive_enabled).lower())
        conf.set("spark.sql.adaptive.coalescePartitions.enabled", 
                str(self.sql_adaptive_coalesce_partitions_enabled).lower())
        
        # 序列化优化
        conf.set("spark.serializer", self.serializer)
        conf.set("spark.kryo.unsafe", str(self.kryo_unsafe).lower())
        
        # 动态分配
        conf.set("spark.dynamicAllocation.enabled", str(self.dynamic_allocation_enabled).lower())
        conf.set("spark.dynamicAllocation.minExecutors", str(self.dynamic_allocation_min_executors))
        conf.set("spark.dynamicAllocation.maxExecutors", str(self.dynamic_allocation_max_executors))
        
        # 其他优化
        conf.set("spark.sql.execution.arrow.pyspark.enabled", "true")
        conf.set("spark.sql.execution.arrow.maxRecordsPerBatch", "10000")
        
        return conf


class SparkPerformanceMonitor:
    """Spark性能监控器"""
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.start_time = time.time()
        self.checkpoints = {}
        
    def checkpoint(self, name: str):
        """记录检查点"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        # 获取Spark指标
        sc = self.spark.sparkContext
        status = sc.statusTracker()
        
        self.checkpoints[name] = {
            'time': current_time,
            'elapsed': elapsed,
            'active_jobs': len(status.getActiveJobIds()),
            'active_stages': len(status.getActiveStageIds()),
            'executor_infos': len(status.getExecutorInfos())
        }
        
    def get_cluster_info(self) -> Dict:
        """获取集群信息"""
        sc = self.spark.sparkContext
        status = sc.statusTracker()
        
        return {
            'application_id': sc.applicationId,
            'application_name': sc.appName,
            'master': sc.master,
            'executor_count': len(status.getExecutorInfos()),
            'default_parallelism': sc.defaultParallelism,
            'spark_version': sc.version
        }
        
    def print_summary(self):
        """打印性能摘要"""
        total_time = time.time() - self.start_time
        cluster_info = self.get_cluster_info()
        
        print(f"\n{'='*60}")
        print("🚀 Spark性能分析报告")
        print(f"{'='*60}")
        print(f"⏱️  总耗时: {total_time:.2f}秒")
        print(f"🖥️  集群信息:")
        print(f"   - 应用ID: {cluster_info['application_id']}")
        print(f"   - 执行器数量: {cluster_info['executor_count']}")
        print(f"   - 并行度: {cluster_info['default_parallelism']}")
        print(f"   - Spark版本: {cluster_info['spark_version']}")
        
        if len(self.checkpoints) > 1:
            print(f"\n📊 各阶段耗时:")
            prev_time = self.start_time
            for name, data in self.checkpoints.items():
                stage_time = data['time'] - prev_time
                percentage = (stage_time / total_time) * 100
                print(f"  - {name}: {stage_time:.2f}s ({percentage:.1f}%)")
                prev_time = data['time']


class DistributedJSONLSampler:
    """分布式JSONL采样器"""
    
    def __init__(self, config: SparkSamplerConfig):
        self.config = config
        self.spark = None
        self.monitor = None
        
    def __enter__(self):
        """上下文管理器入口"""
        self.spark = SparkSession.builder.config(conf=self.config.to_spark_conf()).getOrCreate()
        self.monitor = SparkPerformanceMonitor(self.spark)
        
        # 设置日志级别
        self.spark.sparkContext.setLogLevel("WARN")
        
        print(f"✅ Spark会话已启动")
        print(f"   - 应用名称: {self.config.app_name}")
        print(f"   - 主节点: {self.config.master}")
        print(f"   - 执行器内存: {self.config.executor_memory}")
        
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        if self.spark:
            self.spark.stop()
            print("✅ Spark会话已关闭")
            
    def read_jsonl_files(self, file_pattern: str) -> DataFrame:
        """读取JSONL文件"""
        self.monitor.checkpoint("开始读取文件")
        
        print(f"📁 读取文件模式: {file_pattern}")
        
        # 读取JSON文件
        df = self.spark.read.option("multiline", "false").json(file_pattern)
        
        # 检查数据
        total_count = df.count()
        print(f"📊 总记录数: {total_count:,}")
        
        # 检查source字段
        if 'source' not in df.columns:
            raise ValueError("数据中缺少'source'字段")
            
        # 显示source分布
        source_dist = df.groupBy('source').count().orderBy('count', ascending=False)
        print(f"📈 Source分布:")
        for row in source_dist.collect():
            print(f"   - {row['source']}: {row['count']:,} 条")
            
        self.monitor.checkpoint("文件读取完成")
        return df
        
    def load_previous_records(self, record_files: List[str]) -> Dict[str, List[int]]:
        """加载历史采样记录"""
        if not record_files:
            return {}
            
        print("📚 加载历史采样记录...")
        sampled_indices = {}
        
        for record_file in record_files:
            if not os.path.exists(record_file):
                continue
                
            try:
                with open(record_file, 'r', encoding='utf-8') as f:
                    record_data = json.load(f)
                    
                if 'source_details' in record_data:
                    for source, indices in record_data['source_details'].items():
                        if source not in sampled_indices:
                            sampled_indices[source] = []
                        sampled_indices[source].extend(indices)
                        
            except Exception as e:
                print(f"⚠️ 记录文件 {record_file} 加载失败: {e}")
                
        if sampled_indices:
            total_previous = sum(len(indices) for indices in sampled_indices.values())
            print(f"📋 加载了 {total_previous:,} 条历史采样记录")
            
        return sampled_indices
        
    def filter_previous_samples(self, df: DataFrame, previous_records: Dict[str, List[int]]) -> DataFrame:
        """过滤已采样的数据"""
        if not previous_records:
            return df
            
        self.monitor.checkpoint("开始过滤历史数据")
        
        # 添加行号用于过滤
        window = Window.partitionBy('source').orderBy(monotonically_increasing_id())
        df_with_id = df.withColumn('row_id', row_number().over(window) - 1)
        
        # 创建过滤条件
        filter_condition = None
        for source, indices in previous_records.items():
            if indices:
                source_condition = (col('source') == source) & (~col('row_id').isin(indices))
                if filter_condition is None:
                    filter_condition = source_condition
                else:
                    filter_condition = filter_condition | source_condition
                    
        if filter_condition is not None:
            df_filtered = df_with_id.filter(filter_condition)
        else:
            df_filtered = df_with_id
            
        remaining_count = df_filtered.count()
        print(f"📊 过滤后剩余: {remaining_count:,} 条记录")
        
        self.monitor.checkpoint("历史数据过滤完成")
        return df_filtered.drop('row_id')
        
    def stratified_sample(self, df: DataFrame, sample_ratio: float, random_seed: int = 42) -> Tuple[DataFrame, Dict]:
        """分层采样"""
        self.monitor.checkpoint("开始分层采样")
        
        print(f"🎯 开始分层采样 (比例: {sample_ratio*100:.1f}%)")
        
        # 计算每个source的采样数量
        source_counts = df.groupBy('source').count().collect()
        sampling_plan = {}
        
        print(f"📋 采样计划:")
        for row in source_counts:
            source = row['source']
            total_count = row['count']
            sample_count = max(1, int(total_count * sample_ratio))
            sample_count = min(sample_count, total_count)
            sampling_plan[source] = sample_count
            print(f"   - {source}: {total_count:,} -> {sample_count:,}")
            
        # 执行分层采样
        sampled_dfs = []
        sample_details = {}
        
        for source, sample_count in sampling_plan.items():
            if sample_count > 0:
                source_df = df.filter(col('source') == source)
                
                # 计算采样比例
                total_count = source_df.count()
                if total_count > 0:
                    ratio = min(1.0, sample_count / total_count)
                    sampled_source_df = source_df.sample(fraction=ratio, seed=random_seed)
                    
                    # 确保采样数量正确
                    actual_count = sampled_source_df.count()
                    if actual_count > sample_count:
                        sampled_source_df = sampled_source_df.limit(sample_count)
                        
                    sampled_dfs.append(sampled_source_df)
                    sample_details[source] = sample_count
                    
        # 合并所有采样结果
        if sampled_dfs:
            sampled_df = sampled_dfs[0]
            for df_part in sampled_dfs[1:]:
                sampled_df = sampled_df.union(df_part)
        else:
            sampled_df = df.limit(0)  # 空DataFrame
            
        total_sampled = sampled_df.count()
        print(f"✅ 采样完成，共 {total_sampled:,} 条数据")
        
        self.monitor.checkpoint("分层采样完成")
        return sampled_df, sample_details
        
    def write_results(self, sampled_df: DataFrame, output_prefix: str, 
                     max_lines_per_file: int = 10000) -> List[str]:
        """写入采样结果"""
        self.monitor.checkpoint("开始写入结果")
        
        total_count = sampled_df.count()
        num_partitions = max(1, (total_count + max_lines_per_file - 1) // max_lines_per_file)
        
        print(f"💾 写入 {total_count:,} 条记录到 {num_partitions} 个分区")
        
        # 重新分区以控制输出文件数量
        output_df = sampled_df.coalesce(num_partitions)
        
        # 写入JSON文件
        output_path = f"{output_prefix}_spark"
        output_df.write.mode('overwrite').option("compression", "none").json(output_path)
        
        # 获取输出文件列表
        output_files = []
        output_dir = Path(output_path)
        if output_dir.exists():
            for file_path in output_dir.glob("part-*.json"):
                # 重命名文件
                new_name = f"{output_prefix}_part{len(output_files)+1:03d}.jsonl"
                file_path.rename(file_path.parent.parent / new_name)
                output_files.append(new_name)
                
        print(f"✅ 结果已写入 {len(output_files)} 个文件")
        
        self.monitor.checkpoint("结果写入完成")
        return output_files
        
    def save_sampling_record(self, sample_details: Dict, output_files: List[str], 
                           record_file: str, sample_ratio: float, random_seed: int):
        """保存采样记录"""
        record_data = {
            'timestamp': datetime.now().isoformat(),
            'sample_ratio': sample_ratio,
            'random_seed': random_seed,
            'total_sampled': sum(sample_details.values()),
            'output_files': output_files,
            'source_details': sample_details,
            'spark_config': {
                'app_name': self.config.app_name,
                'master': self.config.master,
                'executor_memory': self.config.executor_memory,
                'driver_memory': self.config.driver_memory
            }
        }
        
        with open(record_file, 'w', encoding='utf-8') as f:
            json.dump(record_data, f, ensure_ascii=False, indent=2)
            
        print(f"📄 采样记录已保存到: {record_file}")


def main():
    parser = argparse.ArgumentParser(description='🚀 Spark分布式JSONL文件采样工具')
    parser.add_argument('--input', '-i', default='*.jsonl',
                        help='输入文件匹配模式 (默认: *.jsonl)')
    parser.add_argument('--output', '-o', default='sampled_data',
                        help='输出文件前缀 (默认: sampled_data)')
    parser.add_argument('--ratio', '-r', type=float, default=0.1,
                        help='抽样比例 (默认: 0.1 即10%%)')
    parser.add_argument('--seed', '-s', type=int, default=42,
                        help='随机种子 (默认: 42)')
    parser.add_argument('--record', default='sampling_record_spark.json',
                        help='本次索引记录文件名')
    parser.add_argument('--previous-records', nargs='*', default=[],
                        help='之前的采样记录文件路径列表')
    parser.add_argument('--max-lines', type=int, default=10000,
                        help='每个输出文件的最大行数 (默认: 10,000)')
    
    # Spark配置参数
    parser.add_argument('--master', default='local[*]',
                        help='Spark主节点 (默认: local[*])')
    parser.add_argument('--executor-memory', default='4g',
                        help='执行器内存 (默认: 4g)')
    parser.add_argument('--driver-memory', default='2g',
                        help='驱动器内存 (默认: 2g)')
    parser.add_argument('--executor-cores', default='2',
                        help='执行器核心数 (默认: 2)')
    
    args = parser.parse_args()
    
    if not 0 < args.ratio <= 1.0:
        print("❌ 错误: 抽样比例必须在 0 和 1 之间")
        return
        
    # 创建Spark配置
    config = SparkSamplerConfig()
    config.master = args.master
    config.executor_memory = args.executor_memory
    config.driver_memory = args.driver_memory
    config.executor_cores = args.executor_cores
    
    start_time = time.time()
    
    try:
        with DistributedJSONLSampler(config) as sampler:
            # 读取数据
            print("=" * 60)
            print("🚀 第一步: 分布式数据读取")
            print("=" * 60)
            df = sampler.read_jsonl_files(args.input)
            
            # 加载历史记录
            print("\n" + "=" * 60)
            print("📚 第二步: 加载历史记录")
            print("=" * 60)
            previous_records = sampler.load_previous_records(args.previous_records)
            
            # 过滤已采样数据
            print("\n" + "=" * 60)
            print("🔍 第三步: 过滤历史数据")
            print("=" * 60)
            filtered_df = sampler.filter_previous_samples(df, previous_records)
            
            # 分层采样
            print("\n" + "=" * 60)
            print("🎯 第四步: 分布式采样")
            print("=" * 60)
            sampled_df, sample_details = sampler.stratified_sample(
                filtered_df, args.ratio, args.seed
            )
            
            # 写入结果
            print("\n" + "=" * 60)
            print("💾 第五步: 分布式写入")
            print("=" * 60)
            output_files = sampler.write_results(sampled_df, args.output, args.max_lines)
            
            # 保存记录
            sampler.save_sampling_record(
                sample_details, output_files, args.record, args.ratio, args.seed
            )
            
            # 性能报告
            total_time = time.time() - start_time
            total_sampled = sum(sample_details.values())
            
            print("\n" + "=" * 60)
            print("🎉 Spark分布式采样完成!")
            print("=" * 60)
            print(f"⏱️  总耗时: {total_time:.2f}秒")
            print(f"🔥 处理速度: {df.count() / total_time:.0f} 条/秒")
            print(f"📊 原始数据: {df.count():,} 条")
            print(f"📈 本次采样: {total_sampled:,} 条")
            print(f"📁 输出文件: {len(output_files)} 个")
            
            # 打印性能摘要
            sampler.monitor.print_summary()
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
