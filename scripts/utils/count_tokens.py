#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSONL批量Token统计脚本
支持读取目录下所有jsonl文件，统计每行tokens，并支持阈值过滤
"""

import argparse
import sys
import os
import json
import glob
from typing import List, Dict, Any, Optional
from pathlib import Path


def load_tokenizer(tokenizer_path: str):
    """从指定路径加载tokenizer"""
    if not os.path.exists(tokenizer_path):
        raise FileNotFoundError(f"Tokenizer路径不存在: {tokenizer_path}")

    try:
        # 尝试HuggingFace tokenizer
        from transformers import AutoTokenizer
        print(f"正在加载HuggingFace tokenizer: {tokenizer_path}")
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
        return tokenizer, "huggingface"

    except Exception as e:
        try:
            # 尝试SentencePiece
            import sentencepiece as spm

            model_files = [f for f in os.listdir(tokenizer_path) if f.endswith('.model')]
            if not model_files:
                raise FileNotFoundError("未找到.model文件")

            model_file = os.path.join(tokenizer_path, model_files[0])
            print(f"正在加载SentencePiece模型: {model_file}")

            sp = spm.SentencePieceProcessor()
            sp.load(model_file)
            return sp, "sentencepiece"

        except Exception as e2:
            raise Exception(f"加载tokenizer失败 - HF: {e}, SP: {e2}")


def count_tokens(tokenizer, tokenizer_type: str, text: str) -> int:
    """统计文本的token数量"""
    if tokenizer_type == "huggingface":
        tokens = tokenizer.encode(text, add_special_tokens=True)
        return len(tokens)
    elif tokenizer_type == "sentencepiece":
        tokens = tokenizer.encode_as_pieces(text)
        return len(tokens)
    else:
        return len(text.split())


def extract_text_from_json(data: Dict[Any, Any], text_fields: List[str]) -> str:
    """从JSON对象中提取文本内容"""
    texts = []

    for field in text_fields:
        if field in data:
            value = data[field]
            if isinstance(value, str):
                texts.append(value)
            elif isinstance(value, (list, dict)):
                texts.append(str(value))

    return " ".join(texts)


def process_jsonl_file(file_path: str, tokenizer, tokenizer_type: str,
                       text_fields: List[str], min_tokens: Optional[int] = None) -> List[Dict]:
    """处理单个JSONL文件"""
    results = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    data = json.loads(line)
                    text = extract_text_from_json(data, text_fields)

                    if not text.strip():
                        continue

                    token_count = count_tokens(tokenizer, tokenizer_type, text)

                    result = {
                        'file': os.path.basename(file_path),
                        'line': line_num,
                        'text': text[:100] + '...' if len(text) > 100 else text,
                        'char_count': len(text),
                        'token_count': token_count,
                        'data': data  # 保存原始数据
                    }

                    # 应用token阈值过滤（保留超过阈值的）
                    if min_tokens is None or token_count > min_tokens:
                        results.append(result)

                except json.JSONDecodeError as e:
                    print(f"警告: {file_path}:{line_num} JSON解析失败: {e}")
                    continue

    except Exception as e:
        print(f"错误: 处理文件 {file_path} 失败: {e}")

    return results


def main():
    parser = argparse.ArgumentParser(description="批量统计JSONL文件中每行的token数量")
    parser.add_argument("tokenizer_path", help="tokenizer模型文件夹路径")
    parser.add_argument("data_dir", help="包含JSONL文件的目录路径")
    parser.add_argument("--min-tokens", "-m", type=int, help="最小token数阈值，只保留超过此值的行")
    parser.add_argument("--text-fields", "-f", nargs="+", default=["text", "content", "input", "output"],
                        help="JSON中包含文本内容的字段名 (默认: text content input output)")
    parser.add_argument("--output", "-o", type=str, help="输出结果到文件")
    parser.add_argument("--stats", "-s", action="store_true", help="显示统计信息")
    parser.add_argument("--save-filtered", type=str, help="将过滤后的数据保存到指定文件")

    args = parser.parse_args()

    # 检查数据目录
    if not os.path.exists(args.data_dir):
        print(f"错误: 数据目录不存在: {args.data_dir}")
        return 1

    # 加载tokenizer
    try:
        tokenizer, tokenizer_type = load_tokenizer(args.tokenizer_path)
        print(f"Tokenizer类型: {tokenizer_type}")
    except Exception as e:
        print(f"错误: {e}")
        return 1

    # 查找所有JSONL文件
    jsonl_pattern = os.path.join(args.data_dir, "*.jsonl")
    jsonl_files = glob.glob(jsonl_pattern)

    if not jsonl_files:
        print(f"警告: 在 {args.data_dir} 中未找到JSONL文件")
        return 1

    print(f"\n找到 {len(jsonl_files)} 个JSONL文件:")
    for f in jsonl_files:
        print(f"  - {os.path.basename(f)}")

    print(f"\n使用文本字段: {', '.join(args.text_fields)}")
    if args.min_tokens:
        print(f"Token阈值: 保留超过 {args.min_tokens} tokens的行")

    # 处理所有文件
    all_results = []
    total_lines = 0
    filtered_lines = 0

    for jsonl_file in jsonl_files:
        print(f"\n处理文件: {os.path.basename(jsonl_file)}")
        results = process_jsonl_file(jsonl_file, tokenizer, tokenizer_type,
                                     args.text_fields, args.min_tokens)

        file_total = len(results)
        total_lines += file_total

        if args.min_tokens:
            # 统计被过滤的行数（小于等于阈值的）
            with open(jsonl_file, 'r', encoding='utf-8') as f:
                original_count = sum(1 for line in f if line.strip())
            filtered_count = original_count - file_total
            filtered_lines += filtered_count
            print(
                f"  原始行数: {original_count}, 保留行数: {file_total} (超过{args.min_tokens}tokens), 过滤行数: {filtered_count}")
        else:
            print(f"  处理行数: {file_total}")

        all_results.extend(results)

    if not all_results:
        print("\n没有找到有效数据")
        return 1

    # 显示统计信息
    if args.stats:
        token_counts = [r['token_count'] for r in all_results]
        char_counts = [r['char_count'] for r in all_results]

        print(f"\n=== 统计信息 ===")
        print(f"总文件数: {len(jsonl_files)}")
        print(f"总行数: {len(all_results)}")
        if args.min_tokens:
            print(f"过滤行数: {filtered_lines} (token数≤{args.min_tokens})")
        print(f"Token统计:")
        print(f"  总计: {sum(token_counts):,}")
        print(f"  平均: {sum(token_counts) / len(token_counts):.1f}")
        print(f"  最小: {min(token_counts)}")
        print(f"  最大: {max(token_counts)}")
        print(f"字符统计:")
        print(f"  总计: {sum(char_counts):,}")
        print(f"  平均: {sum(char_counts) / len(char_counts):.1f}")

    # 输出详细结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            for result in all_results:
                output_data = {
                    'file': result['file'],
                    'line': result['line'],
                    'char_count': result['char_count'],
                    'token_count': result['token_count'],
                    'text_preview': result['text']
                }
                f.write(json.dumps(output_data, ensure_ascii=False) + '\n')
        print(f"\n结果已保存到: {args.output}")

    # 保存过滤后的原始数据（超过阈值的长文本）
    if args.save_filtered:
        with open(args.save_filtered, 'w', encoding='utf-8') as f:
            for result in all_results:
                f.write(json.dumps(result['data'], ensure_ascii=False) + '\n')
        print(f"超过阈值的长文本数据已保存到: {args.save_filtered}")

    # 显示前几行结果
    print(f"\n=== 前10行结果 ===")
    for result in all_results[:10]:
        print(f"{result['file']}:{result['line']} | {result['token_count']} tokens | {result['text']}")

    if len(all_results) > 10:
        print(f"... 还有 {len(all_results) - 10} 行")

    return 0


if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("用法示例:")
        print("python token_counter.py /path/to/tokenizer /path/to/jsonl_dir")
        print("python token_counter.py /path/to/tokenizer /path/to/jsonl_dir --min-tokens 2048 --stats")
        print(
            "python token_counter.py /path/to/tokenizer /path/to/jsonl_dir --text-fields text content --output results.jsonl")
        sys.exit(0)
    else:
        sys.exit(main())
