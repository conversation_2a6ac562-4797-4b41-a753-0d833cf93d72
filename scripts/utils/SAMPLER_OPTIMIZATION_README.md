# 🚀 抽样脚本性能优化报告

## 📊 优化概述

本次优化针对原有的 `sampler.py` 脚本进行了全面的性能提升，创建了 `sampler_optimized.py` 超级优化版本。

## 🎯 主要优化点

### 1. **JSON解析性能优化** 🔥
- **使用orjson库**: 比标准json库快2-5倍
- **批量解析**: 减少函数调用开销
- **错误处理优化**: 更高效的异常处理机制

```python
# 原版
data = json.loads(line)

# 优化版
data = orjson.loads(line)  # 2-5倍性能提升
```

### 2. **文件I/O性能优化** 📁
- **智能文件读取策略**: 小文件直接读取，大文件使用内存映射
- **优化的内存映射**: 使用更大的块大小(64KB)减少系统调用
- **缓冲区优化**: 2MB写入缓冲区提升写入性能

```python
# 优化的文件读取
def read_file_ultra_fast(file_path: str):
    file_size = os.path.getsize(file_path)
    
    # 小文件直接读取
    if file_size < 5 * 1024 * 1024:  # 5MB以下
        # 直接读取策略
    else:
        # 优化的内存映射策略
        chunk_size = 64 * 1024  # 64KB块
```

### 3. **内存管理优化** 💾
- **预分配数据结构**: 减少动态内存分配
- **批量处理**: 50,000条记录批量处理减少内存碎片
- **智能垃圾回收**: 在关键点强制垃圾回收
- **内存使用监控**: 实时监控峰值内存使用

```python
# 预分配结果数组
sampled_data = [None] * total_to_sample

# 批量处理
batch_size = 50000
for batch in process_in_batches(data, batch_size):
    # 批量处理逻辑
```

### 4. **并发处理优化** ⚡
- **智能线程数**: 根据文件数量和CPU核心数动态调整
- **负载均衡**: 大文件优先处理，更好的任务分配
- **锁优化**: 减少锁竞争，使用更粗粒度的锁

```python
# 智能并发数计算
optimal_workers = min(len(files), cpu_count * 3, 20)

# 文件按大小排序，大文件优先
files.sort(key=lambda f: os.path.getsize(f), reverse=True)
```

### 5. **进度监控优化** 📈
- **平滑速率计算**: 使用滑动窗口计算平均速率
- **减少更新频率**: 2秒更新间隔减少I/O开销
- **详细性能分析**: 各阶段耗时和资源使用统计

### 6. **算法优化** 🧮
- **集合操作优化**: 使用集合差集快速计算剩余数据
- **numpy随机采样**: 如果可用，使用numpy的高效随机采样
- **索引管理优化**: 更高效的索引计算和管理

## 📈 性能提升预期

| 优化项目 | 预期提升 | 说明 |
|---------|---------|------|
| JSON解析 | 2-5倍 | 使用orjson库 |
| 文件I/O | 1.5-3倍 | 优化的内存映射和缓冲 |
| 内存使用 | 20-40% | 减少内存分配和碎片 |
| 并发效率 | 1.2-2倍 | 更好的负载均衡 |
| 整体性能 | 2-4倍 | 综合优化效果 |

## 🛠️ 使用方法

### 1. 安装和配置
```bash
# 运行安装脚本
python setup_optimized_sampler.py
```

### 2. 基本使用
```bash
# 使用优化版本
python sampler_optimized.py --input "*.jsonl" --output sampled_data --ratio 0.1

# 启用性能分析
python sampler_optimized.py --profile
```

### 3. 性能对比测试
```bash
# 运行性能对比测试
python benchmark_sampler.py --test-files 5 --lines-per-file 20000
```

## 📊 性能监控功能

### 实时监控
- **内存使用**: 实时监控峰值内存
- **CPU使用率**: 平均CPU使用率统计
- **处理速度**: 动态计算处理速率
- **各阶段耗时**: 详细的阶段性能分析

### 性能报告
```
🚀 性能分析报告
============================================================
⏱️  总耗时: 45.23秒
💾 峰值内存: 1,234.5MB
🖥️  CPU核心数: 8
🔥 处理速度: 22,150 条/秒

📊 各阶段耗时:
  - 数据读取: 25.1s (55.5%)
  - 采样计算: 8.7s (19.2%)
  - 文件写入: 11.4s (25.2%)
```

## 🔧 配置选项

### 性能相关参数
- `--max-workers`: 最大线程数 (默认: 自动)
- `--max-lines`: 每个输出文件最大行数
- `--profile`: 启用详细性能分析

### 高级配置
- 批处理大小: 50,000条记录
- 文件读取块大小: 64KB
- 写入缓冲区: 2MB
- 进度更新间隔: 2秒

## 🎯 适用场景

### 最佳性能场景
- **大型数据集**: 百万级以上记录
- **多文件处理**: 数十个JSONL文件
- **高频采样**: 需要频繁进行数据采样
- **资源充足**: 多核CPU + 充足内存

### 性能提升明显的情况
- 原始数据量 > 100万条
- 文件数量 > 10个
- 单文件大小 > 100MB
- 需要重复采样操作

## 📋 依赖要求

### 必需依赖
- Python 3.7+
- psutil (系统监控)

### 可选依赖 (性能提升)
- orjson (JSON解析加速)
- numpy (随机采样加速)

## 🚨 注意事项

### 内存使用
- 大数据集处理时监控内存使用
- 建议至少8GB内存处理大型数据集
- 可通过调整批处理大小控制内存使用

### 磁盘空间
- 确保有足够空间存储输出文件
- 临时文件可能占用额外空间
- 建议使用SSD提升I/O性能

### 兼容性
- 与原版sampler.py完全兼容
- 输出格式保持一致
- 支持所有原有参数

## 🔄 迁移指南

### 从原版迁移
1. 安装依赖: `python setup_optimized_sampler.py`
2. 替换脚本: 使用 `sampler_optimized.py`
3. 保持参数: 所有原有参数继续有效
4. 验证结果: 使用相同随机种子确保结果一致

### 性能验证
```bash
# 运行对比测试
python benchmark_sampler.py

# 检查结果一致性
diff original_output.jsonl optimized_output.jsonl
```

## 📞 技术支持

如果在使用过程中遇到问题：
1. 检查依赖安装: `python setup_optimized_sampler.py`
2. 运行性能测试: `python benchmark_sampler.py`
3. 查看详细日志: 使用 `--profile` 参数
4. 调整参数: 根据系统资源调整 `--max-workers`

---

**总结**: 优化版抽样脚本通过多方面的性能优化，预期可实现2-4倍的整体性能提升，特别适合大规模数据处理场景。
