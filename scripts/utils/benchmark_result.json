{"test_config": {"test_files": 5, "lines_per_file": 50000, "ratio": 0.1, "max_workers": null}, "original_result": {"success": true, "total_time": 0.8350229263305664, "peak_memory_mb": 20.46875, "avg_cpu_percent": 0.15, "processing_speed": 331187.0, "total_data": 250000, "sampled_data": 25000, "stdout": "============================================================\n第一步: 极速并行读取\n============================================================\n找到 5 个JSONL文件\n快速估算数据量...\n预估总行数: 258,929\n使用 5 个线程并行读取\n\n读取文件: ████░░░░░░░░░░░░░░░░ 20.0% (1/5) 2/s ETA: 2s\n读取文件: ████████████████████ 100.0% (5/5) 7/s ETA: 0s\n✓ 读取文件 completed in 0.7s\n\n总共读取 250,000 条有效数据\n发现 4 个不同的source:\n  - source_A: 62,500 条数据\n  - source_B: 62,500 条数据\n  - source_C: 62,500 条数据\n  - source_D: 62,500 条数据\n\n============================================================\n第二步: 加载历史记录\n============================================================\n\n============================================================\n第三步: 计算剩余数据\n============================================================\n  - source_A: 62,500 总数据, 0 已采样, 62,500 剩余\n  - source_B: 62,500 总数据, 0 已采样, 62,500 剩余\n  - source_C: 62,500 总数据, 0 已采样, 62,500 剩余\n  - source_D: 62,500 总数据, 0 已采样, 62,500 剩余\n\n============================================================\n第四步: 高速采样\n============================================================\n\n计算采样数量 (比例: 10.0%):\n  - source_A: 62,500 -> 6,250\n  - source_B: 62,500 -> 6,250\n  - source_C: 62,500 -> 6,250\n  - source_D: 62,500 -> 6,250\n\n开始采样，总计 25,000 条数据...\n\n采样进度: █████░░░░░░░░░░░░░░░ 25.0% (1/4) 438/s ETA: 0s\n采样进度: ████████████████████ 100.0% (4/4) 481/s ETA: 0s\n✓ 采样进度 completed in 0.0s\n\n采样完成，共 25,000 条数据\n\n============================================================\n第五步: 高速写入\n============================================================\n\n分割为 3 个文件...\n\n写入文件: ██████░░░░░░░░░░░░░░ 33.3% (1/3) 38/s ETA: 0s\n写入文件: ████████████████████ 100.0% (3/3) 47/s ETA: 0s\n✓ 写入文件 completed in 0.1s\n\n============================================================\n🚀 极速采样完成!\n============================================================\n⏱️  总耗时: 0.75秒\n🔥 处理速度: 331187 条/秒\n📊 原始数据: 250,000 条\n📈 本次采样: 25,000 条\n📁 输出文件: 3 个\n", "stderr": "", "memory_timeline": [[0.00011682510375976562, 20.46875], [0.5038671493530273, 20.46875]], "cpu_timeline": [[0.00011682510375976562, 0.0], [0.5038671493530273, 0.3]]}, "optimized_result": {"success": true, "total_time": 0.633331298828125, "peak_memory_mb": 20.53125, "avg_cpu_percent": 0.15, "processing_speed": 467134.0, "total_data": 250000, "sampled_data": 25000, "stdout": "✓ 使用orjson高性能JSON库\n============================================================\n🚀 第一步: 超级并行读取\n============================================================\n找到 5 个JSONL文件\n智能估算数据量...\n预估总行数: 258,930\n使用 5 个线程并行处理\n\n读取文件: ██████░░░░░░░░░░░░░░░░░░░░░░░░ 20.0% (1/5) 4/s ETA: 1s\n读取文件: ██████████████████████████████ 100.0% (5/5) 8/s ETA: 0s\n✓ 读取文件 完成 (0.4s)\n\n总共读取 250,000 条有效数据\n发现 4 个不同的source:\n  - source_A: 62,500 条数据\n  - source_B: 62,500 条数据\n  - source_C: 62,500 条数据\n  - source_D: 62,500 条数据\n\n============================================================\n🚀 性能分析报告\n============================================================\n⏱️  总耗时: 0.40秒\n💾 峰值内存: 282.2MB\n🖥️  CPU核心数: 10\n\n📊 各阶段耗时:\n  - 开始处理: 0.00s (0.0%)\n  - 文件发现完成: 0.00s (0.0%)\n  - 行数估算完成: 0.00s (0.5%)\n  - 开始并行读取: 0.00s (0.0%)\n  - 并行读取完成: 0.40s (99.5%)\n  - 数据统计完成: 0.00s (0.0%)\n\n============================================================\n📚 第二步: 加载历史记录\n============================================================\n\n============================================================\n🧮 第三步: 计算剩余数据\n============================================================\n  - source_A: 62,500 总数据, 0 已采样, 62,500 剩余\n  - source_B: 62,500 总数据, 0 已采样, 62,500 剩余\n  - source_C: 62,500 总数据, 0 已采样, 62,500 剩余\n  - source_D: 62,500 总数据, 0 已采样, 62,500 剩余\n\n============================================================\n🎯 第四步: 超快采样\n============================================================\n\n计算采样数量 (比例: 10.0%):\n  - source_A: 62,500 -> 6,250\n  - source_B: 62,500 -> 6,250\n  - source_C: 62,500 -> 6,250\n  - source_D: 62,500 -> 6,250\n\n开始采样，总计 25,000 条数据...\n\n采样进度: ███████░░░░░░░░░░░░░░░░░░░░░░░ 25.0% (1/4) 19/s ETA: 0s\n采样进度: ██████████████████████████████ 100.0% (4/4) 221/s ETA: 0s\n✓ 采样进度 完成 (0.1s)\n\n采样完成，共 25,000 条数据\n\n============================================================\n💾 第五步: 超快写入\n============================================================\n\n分割为 3 个文件...\n\n写入文件: ██████████░░░░░░░░░░░░░░░░░░░░ 33.3% (1/3) 78/s ETA: 0s\n写入文件: ██████████████████████████████ 100.0% (3/3) 59/s ETA: 0s\n✓ 写入文件 完成 (0.0s)\n\n============================================================\n🎉 超级采样完成!\n============================================================\n⏱️  总耗时: 0.54秒\n🔥 处理速度: 467134 条/秒\n💾 峰值内存: 321.8MB\n📊 原始数据: 250,000 条\n📈 本次采样: 25,000 条\n📁 输出文件: 3 个\n", "stderr": "", "memory_timeline": [[0.0002429485321044922, 20.53125], [0.5021638870239258, 20.53125]], "cpu_timeline": [[0.0002429485321044922, 0.0], [0.5021638870239258, 0.3]]}, "comparison": {"valid_comparison": true, "time_improvement_percent": 24.154022739083015, "memory_improvement_percent": -0.3053435114503817, "speed_improvement_percent": 41.04841071660422, "original_time": 0.8350229263305664, "optimized_time": 0.633331298828125, "original_memory": 20.46875, "optimized_memory": 20.53125, "original_speed": 331187.0, "optimized_speed": 467134.0}, "timestamp": 1757343215.813947}