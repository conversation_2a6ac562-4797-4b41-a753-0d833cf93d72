# file_paths.jsonl

#!/bin/bash
# 文件路径转JSONL脚本
# 功能: 查找指定类型的文件并生成JSONL格式的文件路径列表

# 查找CSG行业数据目录下的所有PDF和DOC文件
# ./file_to_jsonl.sh -t pdf,doc,docx -d ./CSG行业数据 -o csg_documents.jsonl
# 如果你需要绝对路径（便于后续处理）
# ./file_to_jsonl.sh -t pdf,doc,docx -d ./CSG行业数据 -o csg_documents.jsonl --absolute

# 默认参数
TARGET_DIR="."
OUTPUT_FILE="file_paths.jsonl"
FILE_TYPES=()
RECURSIVE=true
RELATIVE_PATH=true

# 显示帮助信息
show_help() {
    cat << EOF
用法: $0 [选项] -t 文件类型1,文件类型2,... [目录路径]

选项:
    -t, --types TYPE1,TYPE2,...  指定要查找的文件类型 (必需)
                                 例如: pdf,doc,docx,txt
    -o, --output FILE           输出JSONL文件名 (默认: file_paths.jsonl)
    -d, --directory DIR         搜索目录 (默认: 当前目录)
    -a, --absolute              使用绝对路径 (默认使用相对路径)
    -s, --shallow               只搜索当前目录，不递归
    -h, --help                  显示此帮助信息

示例:
    $0 -t pdf,doc,docx -o output.jsonl /path/to/directory
    $0 -t txt,md --absolute --shallow
    $0 -t pdf -d ./CSG行业数据 -o csg_files.jsonl

输出格式:
    每行一个JSON对象: {"raw_content": "file_path"}
EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--types)
                if [[ -z "$2" ]]; then
                    echo "错误: -t 参数需要指定文件类型" >&2
                    exit 1
                fi
                IFS=',' read -ra FILE_TYPES <<< "$2"
                shift 2
                ;;
            -o|--output)
                OUTPUT_FILE="$2"
                shift 2
                ;;
            -d|--directory)
                TARGET_DIR="$2"
                shift 2
                ;;
            -a|--absolute)
                RELATIVE_PATH=false
                shift
                ;;
            -s|--shallow)
                RECURSIVE=false
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                echo "错误: 未知选项 $1" >&2
                show_help >&2
                exit 1
                ;;
            *)
                TARGET_DIR="$1"
                shift
                ;;
        esac
    done
}

# 验证参数
validate_args() {
    # 检查是否指定了文件类型
    if [[ ${#FILE_TYPES[@]} -eq 0 ]]; then
        echo "错误: 必须使用 -t 参数指定文件类型" >&2
        show_help >&2
        exit 1
    fi

    # 检查目录是否存在
    if [[ ! -d "$TARGET_DIR" ]]; then
        echo "错误: 目录 '$TARGET_DIR' 不存在" >&2
        exit 1
    fi

    # 检查输出目录是否可写
    OUTPUT_DIR=$(dirname "$OUTPUT_FILE")
    if [[ ! -w "$OUTPUT_DIR" ]]; then
        echo "错误: 无法写入输出目录 '$OUTPUT_DIR'" >&2
        exit 1
    fi
}

# 构建find命令的文件类型参数
build_find_pattern() {
    local patterns=""
    local first=true

    for ext in "${FILE_TYPES[@]}"; do
        # 去除前导点号并转换为小写
        ext=$(echo "$ext" | sed 's/^\.*//' | tr '[:upper:]' '[:lower:]')

        if [[ "$first" == true ]]; then
            patterns="-iname \"*.$ext\""
            first=false
        else
            patterns="$patterns -o -iname \"*.$ext\""
        fi
    done

    # 只有多个条件时才需要括号
    if [[ ${#FILE_TYPES[@]} -gt 1 ]]; then
        echo "\\( $patterns \\)"
    else
        echo "$patterns"
    fi
}

# 转义JSON字符串
escape_json() {
    local str="$1"
    # 转义反斜杠、双引号、换行符等
    echo "$str" | sed 's/\\/\\\\/g; s/"/\\"/g; s/$//' | tr -d '\n\r'
}

# 主函数
main() {
    # 解析和验证参数
    parse_args "$@"
    validate_args

    echo "正在搜索文件..."
    echo "目录: $(realpath "$TARGET_DIR")"
    echo "文件类型: ${FILE_TYPES[*]}"
    echo "输出文件: $OUTPUT_FILE"
    echo "递归搜索: $RECURSIVE"
    echo "使用相对路径: $RELATIVE_PATH"
    echo "----------------------------------------"

    # 构建find命令
    local find_cmd="find \"$TARGET_DIR\""

    # 设置搜索深度
    if [[ "$RECURSIVE" == false ]]; then
        find_cmd="$find_cmd -maxdepth 1"
    fi

    # 只查找文件
    find_cmd="$find_cmd -type f"

    # 添加文件类型模式
    local pattern=$(build_find_pattern)
    find_cmd="$find_cmd $pattern"

    # 临时文件存储找到的文件路径
    local temp_file=$(mktemp)

    # 执行find命令并统计
    eval "$find_cmd" > "$temp_file" 2>/dev/null

    local file_count=$(wc -l < "$temp_file")
    echo "找到 $file_count 个文件"

    if [[ $file_count -eq 0 ]]; then
        echo "警告: 没有找到任何匹配的文件"
        rm "$temp_file"
        exit 0
    fi

    # 清空输出文件
    > "$OUTPUT_FILE"

    # 处理每个文件路径
    local processed=0
    while IFS= read -r filepath; do
        if [[ -n "$filepath" ]]; then
            # 决定使用相对路径还是绝对路径
            if [[ "$RELATIVE_PATH" == true ]]; then
                # 转换为相对于当前工作目录的路径
                if [[ "$filepath" == /* ]]; then
                    # 如果是绝对路径，转换为相对路径
                    local current_dir=$(pwd)
                    filepath=$(realpath --relative-to="$current_dir" "$filepath")
                fi
            else
                # 转换为绝对路径
                filepath=$(realpath "$filepath")
            fi

            # 转义文件路径并生成JSON
            local escaped_path=$(escape_json "$filepath")
            echo "{\"raw_content\": \"$escaped_path\"}" >> "$OUTPUT_FILE"

            ((processed++))

            # 显示进度
            if (( processed % 100 == 0 )); then
                echo "已处理: $processed/$file_count"
            fi
        fi
    done < "$temp_file"

    # 清理临时文件
    rm "$temp_file"

    echo "----------------------------------------"
    echo "完成! 共处理 $processed 个文件"
    echo "输出文件: $OUTPUT_FILE"

    # 显示前几行作为示例
    if [[ $processed -gt 0 ]]; then
        echo
        echo "输出示例 (前5行):"
        head -5 "$OUTPUT_FILE" | while IFS= read -r line; do
            echo "  $line"
        done

        if [[ $processed -gt 5 ]]; then
            echo "  ..."
        fi
    fi
}

# 运行主函数
main "$@"
