#!/usr/bin/env python3
"""
抽样脚本性能对比测试工具
比较原版sampler.py和优化版sampler_optimized.py的性能差异
"""

import os
import sys
import time
import subprocess
import json
import tempfile
import shutil
import argparse
from pathlib import Path
import psutil
import threading
from typing import Dict, List, Tuple


class BenchmarkMonitor:
    """基准测试监控器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.start_time = time.time()
        self.peak_memory = 0
        self.cpu_times = []
        self.memory_samples = []
        self.monitoring = False
        self.monitor_thread = None
        
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.start_time = time.time()
        self.peak_memory = 0
        self.cpu_times = []
        self.memory_samples = []
        
        def monitor_loop():
            while self.monitoring:
                try:
                    memory_mb = self.process.memory_info().rss / 1024 / 1024
                    cpu_percent = self.process.cpu_percent()
                    elapsed = time.time() - self.start_time
                    
                    self.memory_samples.append((elapsed, memory_mb))
                    self.cpu_times.append((elapsed, cpu_percent))
                    self.peak_memory = max(self.peak_memory, memory_mb)
                    
                    time.sleep(0.5)  # 每0.5秒采样一次
                except:
                    break
                    
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
        
    def stop_monitoring(self) -> Dict:
        """停止监控并返回结果"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
            
        total_time = time.time() - self.start_time
        avg_cpu = sum(cpu for _, cpu in self.cpu_times) / len(self.cpu_times) if self.cpu_times else 0
        
        return {
            'total_time': total_time,
            'peak_memory_mb': self.peak_memory,
            'avg_cpu_percent': avg_cpu,
            'memory_samples': self.memory_samples,
            'cpu_samples': self.cpu_times
        }


def create_test_data(output_dir: str, num_files: int = 3, lines_per_file: int = 10000) -> List[str]:
    """创建测试数据"""
    print(f"创建测试数据: {num_files} 个文件，每个 {lines_per_file} 行")
    
    test_files = []
    sources = ['source_A', 'source_B', 'source_C', 'source_D']
    
    for file_idx in range(num_files):
        file_path = os.path.join(output_dir, f"test_data_{file_idx + 1}.jsonl")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            for line_idx in range(lines_per_file):
                data = {
                    'id': f"file{file_idx}_line{line_idx}",
                    'source': sources[line_idx % len(sources)],
                    'content': f"这是测试数据 {file_idx}-{line_idx} " * 10,  # 增加数据大小
                    'metadata': {
                        'file_index': file_idx,
                        'line_index': line_idx,
                        'timestamp': time.time()
                    }
                }
                f.write(json.dumps(data, ensure_ascii=False) + '\n')
                
        test_files.append(file_path)
        
    print(f"测试数据创建完成: {len(test_files)} 个文件")
    return test_files


def run_sampler_benchmark(script_path: str, input_pattern: str, output_prefix: str, 
                         ratio: float = 0.1, max_workers: int = None) -> Dict:
    """运行采样器基准测试"""
    
    cmd = [
        sys.executable, script_path,
        '--input', input_pattern,
        '--output', output_prefix,
        '--ratio', str(ratio),
        '--seed', '42'
    ]
    
    if max_workers:
        cmd.extend(['--max-workers', str(max_workers)])
        
    print(f"运行命令: {' '.join(cmd)}")
    
    # 启动监控
    monitor = BenchmarkMonitor()
    monitor.start_monitoring()
    
    start_time = time.time()
    
    try:
        # 运行采样器
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        end_time = time.time()
        
        # 停止监控
        stats = monitor.stop_monitoring()
        
        # 解析输出中的性能信息
        output_lines = result.stdout.split('\n')
        processing_speed = 0
        total_data = 0
        sampled_data = 0
        
        for line in output_lines:
            if '处理速度:' in line:
                try:
                    speed_str = line.split('处理速度:')[1].split('条/秒')[0].strip()
                    processing_speed = float(speed_str)
                except:
                    pass
            elif '原始数据:' in line:
                try:
                    data_str = line.split('原始数据:')[1].split('条')[0].strip().replace(',', '')
                    total_data = int(data_str)
                except:
                    pass
            elif '本次采样:' in line:
                try:
                    sample_str = line.split('本次采样:')[1].split('条')[0].strip().replace(',', '')
                    sampled_data = int(sample_str)
                except:
                    pass
        
        return {
            'success': result.returncode == 0,
            'total_time': end_time - start_time,
            'peak_memory_mb': stats['peak_memory_mb'],
            'avg_cpu_percent': stats['avg_cpu_percent'],
            'processing_speed': processing_speed,
            'total_data': total_data,
            'sampled_data': sampled_data,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'memory_timeline': stats['memory_samples'],
            'cpu_timeline': stats['cpu_samples']
        }
        
    except subprocess.TimeoutExpired:
        monitor.stop_monitoring()
        return {
            'success': False,
            'error': 'Timeout',
            'total_time': 300,
            'peak_memory_mb': 0,
            'avg_cpu_percent': 0,
            'processing_speed': 0,
            'total_data': 0,
            'sampled_data': 0
        }
    except Exception as e:
        monitor.stop_monitoring()
        return {
            'success': False,
            'error': str(e),
            'total_time': 0,
            'peak_memory_mb': 0,
            'avg_cpu_percent': 0,
            'processing_speed': 0,
            'total_data': 0,
            'sampled_data': 0
        }


def compare_results(original_result: Dict, optimized_result: Dict) -> Dict:
    """比较两个结果"""
    if not original_result['success'] or not optimized_result['success']:
        return {
            'valid_comparison': False,
            'original_success': original_result['success'],
            'optimized_success': optimized_result['success']
        }
    
    time_improvement = (original_result['total_time'] - optimized_result['total_time']) / original_result['total_time'] * 100
    memory_improvement = (original_result['peak_memory_mb'] - optimized_result['peak_memory_mb']) / original_result['peak_memory_mb'] * 100
    speed_improvement = (optimized_result['processing_speed'] - original_result['processing_speed']) / original_result['processing_speed'] * 100
    
    return {
        'valid_comparison': True,
        'time_improvement_percent': time_improvement,
        'memory_improvement_percent': memory_improvement,
        'speed_improvement_percent': speed_improvement,
        'original_time': original_result['total_time'],
        'optimized_time': optimized_result['total_time'],
        'original_memory': original_result['peak_memory_mb'],
        'optimized_memory': optimized_result['peak_memory_mb'],
        'original_speed': original_result['processing_speed'],
        'optimized_speed': optimized_result['processing_speed']
    }


def print_benchmark_results(comparison: Dict, original_result: Dict, optimized_result: Dict):
    """打印基准测试结果"""
    print("\n" + "=" * 80)
    print("🏁 性能对比测试结果")
    print("=" * 80)
    
    if not comparison['valid_comparison']:
        print("❌ 无法进行有效比较")
        print(f"原版脚本成功: {comparison['original_success']}")
        print(f"优化版脚本成功: {comparison['optimized_success']}")
        return
    
    print(f"⏱️  执行时间对比:")
    print(f"   原版: {comparison['original_time']:.2f}秒")
    print(f"   优化版: {comparison['optimized_time']:.2f}秒")
    print(f"   改进: {comparison['time_improvement_percent']:+.1f}%")
    
    print(f"\n💾 内存使用对比:")
    print(f"   原版峰值: {comparison['original_memory']:.1f}MB")
    print(f"   优化版峰值: {comparison['optimized_memory']:.1f}MB")
    print(f"   改进: {comparison['memory_improvement_percent']:+.1f}%")
    
    print(f"\n🚀 处理速度对比:")
    print(f"   原版: {comparison['original_speed']:.0f} 条/秒")
    print(f"   优化版: {comparison['optimized_speed']:.0f} 条/秒")
    print(f"   改进: {comparison['speed_improvement_percent']:+.1f}%")
    
    print(f"\n📊 数据处理统计:")
    print(f"   总数据量: {original_result['total_data']:,} 条")
    print(f"   采样数量: {original_result['sampled_data']:,} 条")
    
    # 总体评估
    print(f"\n🎯 总体性能评估:")
    if comparison['time_improvement_percent'] > 10:
        print("   ✅ 时间性能显著提升")
    elif comparison['time_improvement_percent'] > 0:
        print("   ✅ 时间性能有所提升")
    else:
        print("   ⚠️ 时间性能无明显改善")
        
    if comparison['memory_improvement_percent'] > 10:
        print("   ✅ 内存使用显著优化")
    elif comparison['memory_improvement_percent'] > 0:
        print("   ✅ 内存使用有所优化")
    else:
        print("   ⚠️ 内存使用无明显改善")


def main():
    parser = argparse.ArgumentParser(description='抽样脚本性能对比测试')
    parser.add_argument('--test-files', type=int, default=5,
                        help='测试文件数量 (默认: 5)')
    parser.add_argument('--lines-per-file', type=int, default=20000,
                        help='每个文件的行数 (默认: 20,000)')
    parser.add_argument('--ratio', type=float, default=0.1,
                        help='采样比例 (默认: 0.1)')
    parser.add_argument('--max-workers', type=int, default=None,
                        help='最大工作线程数')
    parser.add_argument('--keep-test-data', action='store_true',
                        help='保留测试数据')
    
    args = parser.parse_args()
    
    # 获取脚本路径
    script_dir = Path(__file__).parent
    original_script = script_dir / 'sampler.py'
    optimized_script = script_dir / 'sampler_optimized.py'
    
    if not original_script.exists():
        print(f"错误: 找不到原版脚本 {original_script}")
        return
        
    if not optimized_script.exists():
        print(f"错误: 找不到优化版脚本 {optimized_script}")
        return
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"使用临时目录: {temp_dir}")
        
        # 创建测试数据
        test_files = create_test_data(temp_dir, args.test_files, args.lines_per_file)
        input_pattern = os.path.join(temp_dir, "*.jsonl")
        
        # 测试原版脚本
        print("\n" + "=" * 60)
        print("🔄 测试原版脚本")
        print("=" * 60)
        original_output = os.path.join(temp_dir, "original_output")
        original_result = run_sampler_benchmark(
            str(original_script), input_pattern, original_output, 
            args.ratio, args.max_workers
        )
        
        # 清理输出文件
        for f in Path(temp_dir).glob("original_output*"):
            f.unlink()
        
        # 测试优化版脚本
        print("\n" + "=" * 60)
        print("🚀 测试优化版脚本")
        print("=" * 60)
        optimized_output = os.path.join(temp_dir, "optimized_output")
        optimized_result = run_sampler_benchmark(
            str(optimized_script), input_pattern, optimized_output,
            args.ratio, args.max_workers
        )
        
        # 比较结果
        comparison = compare_results(original_result, optimized_result)
        print_benchmark_results(comparison, original_result, optimized_result)
        
        # 保存详细结果
        benchmark_result = {
            'test_config': {
                'test_files': args.test_files,
                'lines_per_file': args.lines_per_file,
                'ratio': args.ratio,
                'max_workers': args.max_workers
            },
            'original_result': original_result,
            'optimized_result': optimized_result,
            'comparison': comparison,
            'timestamp': time.time()
        }
        
        result_file = 'benchmark_result.json'
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(benchmark_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细结果已保存到: {result_file}")


if __name__ == "__main__":
    main()
