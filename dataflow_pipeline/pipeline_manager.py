"""
DataFlow Pipeline Manager

This module provides the core pipeline management functionality,
including pipeline execution and error handling.
"""

import json
import datetime
import logging
import math
import tempfile
import shutil
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor, ProcessPoolExecutor, as_completed
import time
import multiprocessing as mp

from dataflow.utils.storage import FileStorage
from dataflow.serving import APILLMServing_request

from .config import PipelineConfig, ConfigManager
from .operators import OperatorFactory, BaseOperator

logger = logging.getLogger(__name__)

def _convert_pdf_to_jsonl(input_files, input_key):
    """Convert PDF files to JSONL
    {"raw_content": "path/to/pdf/file.pdf"}
    {"raw_content": "path/to/pdf/file1.pdf"}
    """
    output_file = Path(input_files[0]).with_suffix('.jsonl')
    for input_file in input_files:
        with open(output_file, 'w') as f:
            f.write(json.dumps({input_key: input_file}) + '\n')

    return [str(output_file)]

class PipelineManager:
    """Core pipeline manager for executing data processing workflows"""
    
    def __init__(self, config: PipelineConfig):
        self.config = config
        self.operators: List[BaseOperator] = []
        self.llm_serving = None
        self.temp_dir = self.config.temp_dir

        # Setup logging
        self._setup_logging()

        # Initialize LLM serving if configured
        if config.llm:
            self._setup_llm_serving()

        # Initialize operators
        self._initialize_operators()
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_level = getattr(logging, self.config.log_level.upper(), logging.INFO)
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(f"{self.config.storage.cache_path}/pipeline-{self.config.name}-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.log")
            ]
        )
    

    def _setup_llm_serving(self):
        """Initialize LLM serving"""
        try:
            self.llm_serving = APILLMServing_request(
                api_url=self.config.llm.api_url,
                model_name=self.config.llm.model_name,
                max_workers=self.config.llm.max_workers
            )
            logger.info(f"LLM serving initialized: {self.config.llm.model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize LLM serving: {e}")
            raise
    
    def _initialize_operators(self):
        """Initialize all operators from configuration"""
        self.operators = []
        
        for op_config in self.config.operators:
            if not op_config.enabled:
                logger.info(f"Skipping disabled operator: {op_config.name}")
                continue
            
            try:
                operator = OperatorFactory.create_operator(
                    operator_type=op_config.type,
                    name=op_config.name,
                    parameters=op_config.parameters,
                    llm_serving=self.llm_serving
                )
                
                if operator.validate_parameters():
                    self.operators.append(operator)
                    logger.info(f"Initialized operator: {op_config.name} ({op_config.type})")
                else:
                    logger.error(f"Invalid parameters for operator: {op_config.name}")
                    
            except Exception as e:
                logger.error(f"Failed to initialize operator {op_config.name}: {e}")
                if self.config.retry_attempts == 0:
                    raise

    def _get_npu_device_allocation(self, num_workers: int) -> Dict[int, int]:
        """Simple NPU device allocation for workers"""
        allocation_map = {}

        if not self.config.npu or not self.config.npu.enabled:
            return allocation_map

        max_devices = self.config.npu.max_devices
        allocation_strategy = self.config.npu.device_allocation

        logger.info(f"Allocating NPU devices: {num_workers} workers, {max_devices} max devices, strategy: {allocation_strategy}")

        for worker_id in range(num_workers):
            if allocation_strategy == "round_robin":
                device_id = worker_id % max_devices
            elif allocation_strategy == "exclusive":
                if worker_id < max_devices:
                    device_id = worker_id
                else:
                    continue  # Skip workers that exceed device count
            else:  # auto
                if num_workers <= max_devices:
                    device_id = worker_id  # exclusive mode
                else:
                    device_id = worker_id % max_devices  # round_robin mode

            allocation_map[worker_id] = device_id

        logger.info(f"NPU device allocation: {allocation_map}")
        return allocation_map

    def _split_jsonl_file(self, file_path: str, num_chunks: int) -> List[str]:
        """Split a JSONL file into multiple chunks for parallel processing"""
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"Input file not found: {file_path}")

        # Create temporary directory for chunks
        if self.temp_dir is None:
            self.temp_dir = tempfile.mkdtemp(prefix="dataflow_chunks_")

        chunk_files = []

        try:
            # Count total lines first
            with open(file_path, 'r', encoding='utf-8') as f:
                total_lines = sum(1 for _ in f)

            if total_lines == 0:
                logger.warning(f"Empty file: {file_path}")
                return []

            # Calculate lines per chunk
            lines_per_chunk = max(1, math.ceil(total_lines / num_chunks))

            logger.info(f"Splitting {file_path} ({total_lines} lines) into {num_chunks} chunks ({lines_per_chunk} lines each)")

            # Split file into chunks
            with open(file_path, 'r', encoding='utf-8') as f:
                chunk_idx = 0
                current_chunk_lines = 0
                current_chunk_file = None

                for line in f:
                    # Start new chunk if needed
                    if current_chunk_lines == 0:
                        if current_chunk_file:
                            current_chunk_file.close()

                        chunk_filename = f"{file_path.stem}_chunk_{chunk_idx:04d}.jsonl"
                        chunk_path = Path(self.temp_dir) / chunk_filename
                        chunk_files.append(str(chunk_path))
                        current_chunk_file = open(chunk_path, 'w', encoding='utf-8')
                        chunk_idx += 1

                    # Write line to current chunk
                    current_chunk_file.write(line)
                    current_chunk_lines += 1

                    # Close chunk if it's full
                    if current_chunk_lines >= lines_per_chunk:
                        current_chunk_lines = 0

                # Close last chunk file
                if current_chunk_file:
                    current_chunk_file.close()

            logger.info(f"Created {len(chunk_files)} chunk files")
            return chunk_files

        except Exception as e:
            logger.error(f"Failed to split file {file_path}: {e}")
            # Clean up on error
            for chunk_file in chunk_files:
                try:
                    Path(chunk_file).unlink(missing_ok=True)
                except:
                    pass
            raise

    def _merge_chunk_results(self, chunk_results: List[str], output_path: str) -> bool:
        """Merge results from chunk processing into final output"""
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            total_lines = 0

            with open(output_path, 'w', encoding='utf-8') as output_file:
                for chunk_result in chunk_results:
                    chunk_path = Path(chunk_result)
                    if chunk_path.exists():
                        with open(chunk_path, 'r', encoding='utf-8') as chunk_file:
                            for line in chunk_file:
                                output_file.write(line)
                                total_lines += 1
                    else:
                        logger.warning(f"Chunk result file not found: {chunk_result}")

            logger.info(f"Merged {len(chunk_results)} chunk results into {output_path} ({total_lines} lines)")
            return True

        except Exception as e:
            logger.error(f"Failed to merge chunk results: {e}")
            return False

    def _cleanup_temp_files(self):
        """Clean up temporary files and directories"""
        if self.temp_dir and Path(self.temp_dir).exists():
            try:
                shutil.rmtree(self.temp_dir)
                logger.info(f"Cleaned up temporary directory: {self.temp_dir}")
            except Exception as e:
                logger.warning(f"Failed to clean up temporary directory {self.temp_dir}: {e}")
            finally:
                self.temp_dir = None

    def execute_pipeline(self, input_files: Optional[List[str]] = None) -> bool:
        """Execute the complete pipeline with parallel processing support"""
        start_time = time.time()

        try:
            logger.info(f"Starting pipeline execution: {self.config.name}")

            # Determine input files
            if input_files is None:
                input_files = self._get_input_files()

            if not input_files:
                logger.error("No input files found")
                return False

            # Process files with parallel support
            success_count = 0
            total_files = len(input_files)

            # Check if we should use parallel processing for large JSONL files
            if (self.config.parallel_workers > 1 and
                len(input_files) == 1 and
                input_files[0].endswith('.jsonl')):
                # Single large JSONL file - split and process in parallel
                success_count = self._execute_parallel_chunks(input_files[0])
                total_files = 1  # We're processing one logical file
            elif self.config.parallel_workers > 1:
                # Multiple files - process files in parallel
                success_count = self._execute_parallel_files(input_files)
            else:
                # Sequential processing
                success_count = self._execute_sequential(input_files)

            # Calculate statistics
            execution_time = time.time() - start_time

            # Log results
            success_rate = (success_count / total_files) * 100 if total_files > 0 else 0
            logger.info(f"Pipeline completed: {success_count}/{total_files} files processed successfully ({success_rate:.1f}%)")
            logger.info(f"Total execution time: {execution_time:.2f} seconds")

            # Clean up temporary files
            self._cleanup_temp_files()

            return success_count == total_files

        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            self._cleanup_temp_files()
            return False
    
    def _get_input_files(self) -> List[str]:
        """Get list of input files to process"""
        input_path = Path(self.config.storage.input_path)
        
        if input_path.is_file():
            return [str(input_path)]
        elif input_path.is_dir():

            # Find pdfs or jsonl files, if pdfs exists, skip jsonl files
            files = list(input_path.glob('*.pdf'))
            if files:
                logger.info(f"Found {len(files)} PDF files in input directory, skip jsonl files")
                return [str(f) for f in files]

            # Find all JSON/JSONL files in directory
            files = []
            for pattern in ['*.json', '*.jsonl']:
                files.extend(input_path.glob(pattern))
            return [str(f) for f in files]
        else:
            logger.error(f"Input path not found: {input_path}")
            return []
    
    def _execute_sequential(self, input_files: List[str]) -> int:
        """Execute pipeline sequentially"""
        success_count = 0
        
        for i, input_file in enumerate(input_files):
            logger.info(f"Processing file {i+1}/{len(input_files)}: {input_file}")
            
            if self._process_single_file(input_file):
                success_count += 1
        
        return success_count
    
    def _execute_parallel_chunks(self, input_file: str) -> int:
        """Execute pipeline by splitting JSONL file into chunks and processing in parallel"""
        try:
            logger.info(f"Processing large JSONL file with {self.config.parallel_workers} parallel workers")

            # Split the JSONL file into chunks
            chunk_files = self._split_jsonl_file(input_file, self.config.parallel_workers)

            if not chunk_files:
                logger.error("Failed to create chunks from input file")
                return 0

            # Process chunks in parallel
            success_count = 0
            chunk_results = []

            # Prepare NPU device allocation if available
            npu_allocation_map = self._get_npu_device_allocation(len(chunk_files))

            with ProcessPoolExecutor(max_workers=self.config.parallel_workers) as executor:
                # Submit chunk processing tasks
                future_to_chunk = {}
                for i, chunk_file in enumerate(chunk_files):
                    # Get NPU device for this worker
                    npu_device_id = npu_allocation_map.get(i, None)

                    future = executor.submit(
                        self._process_chunk_worker,
                        chunk_file,
                        i,
                        self.config,
                        npu_device_id
                    )
                    future_to_chunk[future] = chunk_file

                # Collect results
                completed_chunks = 0
                for future in as_completed(future_to_chunk):
                    chunk_file = future_to_chunk[future]
                    completed_chunks += 1

                    try:
                        result_file = future.result()
                        if result_file:
                            chunk_results.append(result_file)
                            success_count += 1
                            logger.info(f"Chunk {completed_chunks}/{len(chunk_files)} completed: {Path(chunk_file).name}")
                        else:
                            logger.error(f"Chunk processing failed: {Path(chunk_file).name}")
                    except Exception as e:
                        logger.error(f"Chunk processing error for {chunk_file}: {e}")

                    # Update progress
                    progress = (completed_chunks / len(chunk_files)) * 100

            # Merge chunk results if all succeeded
            if success_count == len(chunk_files) and chunk_results:
                if self._merge_chunk_results(chunk_results, self.config.storage.output_path):
                    logger.info("Successfully merged all chunk results")
                    return 1  # One logical file processed successfully
                else:
                    logger.error("Failed to merge chunk results")
                    return 0
            else:
                logger.error(f"Only {success_count}/{len(chunk_files)} chunks processed successfully")
                return 0

        except Exception as e:
            logger.error(f"Parallel chunk processing failed: {e}")
            return 0

    def _execute_parallel_files(self, input_files: List[str]) -> int:
        """Execute pipeline with multiple files in parallel"""
        success_count = 0
        completed_count = 0

        # check input_files type, if pdf, convert to jsonl first
        if input_files[0].endswith('.pdf'):
            logger.info(f"Converting PDF files to JSONL")
            input_files = _convert_pdf_to_jsonl(input_files, input_key="raw_content")

        
        with ThreadPoolExecutor(max_workers=self.config.parallel_workers) as executor:
            # Submit all tasks
            future_to_file = {
                executor.submit(self._process_single_file, input_file): input_file
                for input_file in input_files
            }
            
            # Process completed tasks
            for future in as_completed(future_to_file):
                input_file = future_to_file[future]
                completed_count += 1
                
                try:
                    if future.result():
                        success_count += 1
                        logger.info(f"Successfully processed: {input_file}")
                    else:
                        logger.error(f"Failed to process: {input_file}")
                        
                except Exception as e:
                    logger.error(f"Error processing {input_file}: {e}")
        
        return success_count

    @staticmethod
    def _process_chunk_worker(chunk_file: str, chunk_index: int, config: PipelineConfig, npu_device_id: Optional[int] = None) -> Optional[str]:
        """Worker function to process a single chunk (runs in separate process)"""
        try:
            # Import here to avoid issues with multiprocessing
            import logging
            import sys
            import os
            from pathlib import Path
            from dataflow.utils.storage import FileStorage
            from dataflow.serving import APILLMServing_request

            # Add current directory to path for imports
            current_dir = Path(__file__).parent.parent
            if str(current_dir) not in sys.path:
                sys.path.insert(0, str(current_dir))

            from dataflow_pipeline.operators import OperatorFactory

            # Setup NPU environment if device is allocated
            if npu_device_id is not None:
                os.environ['ASCEND_RT_VISIBLE_DEVICES'] = str(npu_device_id)
                os.environ['ASCEND_DEVICE_ID'] = str(npu_device_id)

            # Setup logging for worker
            logger = logging.getLogger(f"chunk_worker_{chunk_index}")

            # Initialize LLM serving if needed
            llm_serving = None
            if config.llm:
                llm_serving = APILLMServing_request(
                    api_url=config.llm.api_url,
                    model_name=config.llm.model_name,
                    max_workers=1  # Use single worker per chunk to avoid conflicts
                )

            # Initialize operators
            operators = []
            for op_config in config.operators:
                if not op_config.enabled:
                    continue

                operator = OperatorFactory.create_operator(
                    operator_type=op_config.type,
                    name=op_config.name,
                    parameters=op_config.parameters,
                    llm_serving=llm_serving
                )
                operators.append(operator)

            # Create storage for this chunk
            chunk_path = Path(chunk_file)
            output_dir = chunk_path.parent / "results"
            output_dir.mkdir(exist_ok=True)

            result_file = output_dir / f"result_{chunk_index:04d}.jsonl"

            storage = FileStorage(
                first_entry_file_name=chunk_file,
                cache_path=str(output_dir / f"cache_{chunk_index:04d}"),
                file_name_prefix=f"chunk_{chunk_index:04d}_step",
                cache_type="jsonl"
            )

            # Execute operators in sequence
            for operator in operators:
                if not operator.enabled:
                    continue

                retry_count = 0
                while retry_count <= config.retry_attempts:
                    try:
                        success = operator.run(storage=storage.step())
                        if success:
                            break
                        else:
                            retry_count += 1
                            if retry_count <= config.retry_attempts:
                                logger.warning(f"Retrying operator {operator.name} for chunk {chunk_index} (attempt {retry_count})")
                    except Exception as e:
                        retry_count += 1
                        logger.error(f"Operator {operator.name} failed for chunk {chunk_index} (attempt {retry_count}): {e}")

                        if retry_count > config.retry_attempts:
                            logger.error(f"Operator {operator.name} failed for chunk {chunk_index} after {config.retry_attempts} retries")
                            return None

                        time.sleep(1)  # Brief delay before retry

            # Find the final output file from the last step
            cache_dir = Path(storage.cache_path)
            output_files = list(cache_dir.glob("*.jsonl"))

            if output_files:
                # Use the most recent output file
                latest_output = max(output_files, key=lambda p: p.stat().st_mtime)

                # Copy to result file
                shutil.copy2(latest_output, result_file)
                logger.info(f"Chunk {chunk_index} processing completed: {result_file}")
                return str(result_file)
            else:
                logger.error(f"No output files found for chunk {chunk_index}")
                return None

        except Exception as e:
            logger.error(f"Chunk worker {chunk_index} failed: {e}")
            return None

    def _process_single_file(self, input_file: str) -> bool:
        """Process a single input file through the pipeline"""
        try:
            # Create unique storage for this file
            file_name = Path(input_file).stem
            time_stamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            
            storage = FileStorage(
                first_entry_file_name=input_file,
                cache_path=f"{self.config.storage.cache_path}/{time_stamp}",
                file_name_prefix=f"{file_name}_{self.config.storage.file_name_prefix}",
                cache_type=self.config.storage.cache_type
            )
            
            # Execute operators in sequence
            for operator in self.operators:
                if not operator.enabled:
                    continue
                
                retry_count = 0
                while retry_count <= self.config.retry_attempts:
                    try:
                        success = operator.run(storage=storage.step())
                        if success:
                            break
                        else:
                            retry_count += 1
                            if retry_count <= self.config.retry_attempts:
                                logger.warning(f"Retrying operator {operator.name} (attempt {retry_count})")
                            
                    except Exception as e:
                        retry_count += 1
                        logger.error(f"Operator {operator.name} failed (attempt {retry_count}): {e}")
                        
                        if retry_count > self.config.retry_attempts:
                            logger.error(f"Operator {operator.name} failed after {self.config.retry_attempts} retries")
                            return False
                        
                        time.sleep(1)  # Brief delay before retry
            
            logger.info(f"Successfully processed file: {input_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to process file {input_file}: {e}")
            return False
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """Get pipeline information"""
        return {
            'name': self.config.name,
            'description': self.config.description,
            'operators': [op.get_info() for op in self.operators],
            'storage': {
                'input_path': self.config.storage.input_path,
                'output_path': self.config.storage.output_path,
                'cache_path': self.config.storage.cache_path
            },
            'configuration': {
                'batch_size': self.config.batch_size,
                'parallel_workers': self.config.parallel_workers,
                'retry_attempts': self.config.retry_attempts,
                'log_level': self.config.log_level
            }
        }
