"""
DataFlow Pipeline Configuration Management Module

This module provides configuration management for flexible data processing pipelines.
Supports YAML and JSON configuration files with validation and parameter override capabilities.
"""

import yaml
import json
import os
from typing import Dict, Any, List, Optional, Union, Literal
from dataclasses import dataclass, field
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


@dataclass
class StorageConfig:
    """Storage configuration for data input/output"""
    input_path: str
    output_path: str
    cache_path: str = "./cache"
    file_name_prefix: str = "dataflow"
    cache_type: Literal["json", "jsonl", "csv", "parquet", "pickle"] = "jsonl"
    
    def __post_init__(self):
        # Ensure paths exist
        Path(self.cache_path).mkdir(parents=True, exist_ok=True)
        Path(os.path.dirname(self.output_path)).mkdir(parents=True, exist_ok=True)


@dataclass
class LLMConfig:
    """LLM service configuration"""
    api_url: str
    model_name: str
    max_workers: int = 2
    temperature: float = 0.7
    max_tokens: int = 2048
    timeout: int = 60


@dataclass
class OperatorConfig:
    """Individual operator configuration"""
    name: str
    type: str
    enabled: bool = True
    parameters: Dict[str, Any] = field(default_factory=dict)
    input_key: str = "raw_chunk"
    output_key: Optional[str] = None


@dataclass
class NPUConfig:
    """NPU device configuration for parallel processing"""
    enabled: bool = False
    max_devices: int = 8          # Maximum number of NPU devices available
    device_allocation: str = "auto"  # "auto", "round_robin", "exclusive"


@dataclass
class PipelineConfig:
    """Complete pipeline configuration"""
    name: str
    description: str
    storage: StorageConfig
    llm: Optional[LLMConfig] = None
    npu: Optional[NPUConfig] = None
    operators: List[OperatorConfig] = field(default_factory=list)
    batch_size: int = 100
    parallel_workers: int = 1
    retry_attempts: int = 3
    log_level: str = "INFO"
    chunk_processing: bool = True  # Enable automatic chunking for large JSONL files
    max_chunk_size: int = 10000   # Maximum lines per chunk
    temp_dir: Optional[str] = None  # Temporary directory for chunk processing


class ConfigManager:
    """Configuration manager for DataFlow pipelines"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path
        self.config: Optional[PipelineConfig] = None
        
    def load_config(self, config_path: str) -> PipelineConfig:
        """Load configuration from YAML or JSON file"""
        config_path = Path(config_path)
        
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    config_data = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    config_data = json.load(f)
                else:
                    raise ValueError(f"Unsupported config file format: {config_path.suffix}")
                    
            self.config = self._parse_config(config_data)
            logger.info(f"Configuration loaded successfully from {config_path}")
            return self.config
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
    
    def _parse_config(self, config_data: Dict[str, Any]) -> PipelineConfig:
        """Parse configuration data into PipelineConfig object"""
        
        # Parse storage configuration
        storage_data = config_data.get('storage', {})
        storage = StorageConfig(**storage_data)
        
        # Parse LLM configuration (optional)
        llm = None
        if 'llm' in config_data:
            llm = LLMConfig(**config_data['llm'])

        # Parse NPU configuration (optional)
        npu = None
        if 'npu' in config_data:
            npu = NPUConfig(**config_data['npu'])

        # Parse operators configuration
        operators = []
        for op_data in config_data.get('operators', []):
            operators.append(OperatorConfig(**op_data))
        
        # Create pipeline configuration
        pipeline_data = {
            'name': config_data.get('name', 'default_pipeline'),
            'description': config_data.get('description', ''),
            'storage': storage,
            'llm': llm,
            'npu': npu,
            'operators': operators,
            'batch_size': config_data.get('batch_size', 100),
            'parallel_workers': config_data.get('parallel_workers', 1),
            'retry_attempts': config_data.get('retry_attempts', 3),
            'log_level': config_data.get('log_level', 'INFO'),
            'chunk_processing': config_data.get('chunk_processing', True),
            'max_chunk_size': config_data.get('max_chunk_size', 10000)
        }
        
        return PipelineConfig(**pipeline_data)
    
    def save_config(self, config: PipelineConfig, output_path: str):
        """Save configuration to file"""
        output_path = Path(output_path)
        
        # Convert config to dictionary
        config_dict = self._config_to_dict(config)
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                if output_path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
                elif output_path.suffix.lower() == '.json':
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"Unsupported output format: {output_path.suffix}")
                    
            logger.info(f"Configuration saved to {output_path}")
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            raise
    
    def _config_to_dict(self, config: PipelineConfig) -> Dict[str, Any]:
        """Convert PipelineConfig to dictionary"""
        config_dict = {
            'name': config.name,
            'description': config.description,
            'storage': {
                'input_path': config.storage.input_path,
                'output_path': config.storage.output_path,
                'cache_path': config.storage.cache_path,
                'file_name_prefix': config.storage.file_name_prefix,
                'cache_type': config.storage.cache_type
            },
            'operators': [
                {
                    'name': op.name,
                    'type': op.type,
                    'enabled': op.enabled,
                    'parameters': op.parameters,
                    'input_key': op.input_key,
                    'output_key': op.output_key
                }
                for op in config.operators
            ],
            'batch_size': config.batch_size,
            'parallel_workers': config.parallel_workers,
            'retry_attempts': config.retry_attempts,
            'log_level': config.log_level,
            'chunk_processing': config.chunk_processing,
            'max_chunk_size': config.max_chunk_size
        }
        
        if config.llm:
            config_dict['llm'] = {
                'api_url': config.llm.api_url,
                'model_name': config.llm.model_name,
                'max_workers': config.llm.max_workers,
                'temperature': config.llm.temperature,
                'max_tokens': config.llm.max_tokens,
                'timeout': config.llm.timeout
            }

        if config.npu:
            config_dict['npu'] = {
                'enabled': config.npu.enabled,
                'max_devices': config.npu.max_devices,
                'device_allocation': config.npu.device_allocation
            }
        
        return config_dict
    
    def override_parameters(self, overrides: Dict[str, Any]):
        """Override configuration parameters"""
        if not self.config:
            raise ValueError("No configuration loaded")
            
        for key, value in overrides.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                logger.info(f"Override parameter: {key} = {value}")
            else:
                logger.warning(f"Unknown parameter: {key}")
    
    def validate_config(self, config: PipelineConfig) -> bool:
        """Validate configuration"""
        try:
            # Check required fields
            if not config.name:
                raise ValueError("Pipeline name is required")
            
            if not config.storage.input_path:
                raise ValueError("Input path is required")
                
            if not config.storage.output_path:
                raise ValueError("Output path is required")
            
            # Check input file exists
            if not Path(config.storage.input_path).exists():
                raise FileNotFoundError(f"Input file not found: {config.storage.input_path}")
            
            # Validate operators
            for op in config.operators:
                if not op.name or not op.type:
                    raise ValueError(f"Operator name and type are required: {op}")
            
            logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False


def create_default_config() -> PipelineConfig:
    """Create a default configuration template"""
    storage = StorageConfig(
        input_path="./data/input.jsonl",
        output_path="./data/output.jsonl",
        cache_path="./cache",
        file_name_prefix="dataflow_step",
        cache_type="jsonl"
    )
    
    llm = LLMConfig(
        api_url="http://localhost:8008/v1/chat/completions",
        model_name="default-model",
        max_workers=2
    )
    
    operators = [
        OperatorConfig(
            name="text_cleaning",
            type="filter",
            parameters={"min_words": 5, "max_words": 10000}
        ),
        OperatorConfig(
            name="deduplication",
            type="filter",
            parameters={"threshold": 0.9}
        )
    ]
    
    return PipelineConfig(
        name="default_pipeline",
        description="Default data processing pipeline",
        storage=storage,
        llm=llm,
        operators=operators,
        parallel_workers=4,
        chunk_processing=True,
        max_chunk_size=10000
    )
