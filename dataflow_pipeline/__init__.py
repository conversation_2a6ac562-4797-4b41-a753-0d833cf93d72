"""
DataFlow Pipeline - Flexible Data Processing Framework

A comprehensive framework for building and executing data processing pipelines
using the DataFlow library. Supports configurable operators
and batch processing for various data types.

Main Components:
- ConfigManager: Configuration file management
- PipelineManager: Pipeline execution
- OperatorFactory: Operator creation and management
- CLI: Command line interface

Example Usage:
    from dataflow_pipeline import ConfigManager, PipelineManager
    
    # Load configuration
    config_manager = ConfigManager()
    config = config_manager.load_config('config.yaml')
    
    # Run pipeline
    pipeline = PipelineManager(config)
    success = pipeline.execute_pipeline()
"""

__version__ = "1.0.0"
__author__ = "DataFlow Pipeline Team"
__email__ = "<EMAIL>"

# Import main classes for easy access
from .config import (
    ConfigManager,
    PipelineConfig,
    StorageConfig,
    LLMConfig,
    OperatorConfig,
    create_default_config
)

from .pipeline_manager import PipelineManager

from .operators import (
    BaseOperator,
    FilterOperator,
    RefinerOperator,
    GeneratorOperator,
    OperatorFactory,
    TextCleaningOperator,
    PDF2MDOperator,
    ChunkingOperator,
    QAGenerationOperator,
    LLMCleaningOperator,
    BatchProcessingOperator
)

from .cli import DataFlowCLI

# Define what gets imported with "from dataflow_pipeline import *"
__all__ = [
    # Configuration
    'ConfigManager',
    'PipelineConfig',
    'StorageConfig',
    'LLMConfig',
    'OperatorConfig',
    'create_default_config',
    
    # Pipeline Management
    'PipelineManager',
    
    # Operators
    'BaseOperator',
    'FilterOperator',
    'RefinerOperator',
    'GeneratorOperator',
    'OperatorFactory',
    'TextCleaningOperator',
    'PDF2MDOperator',
    'ChunkingOperator',
    'QAGenerationOperator',
    'LLMCleaningOperator',
    'BatchProcessingOperator',
    
    # CLI
    'DataFlowCLI'
]


def get_version():
    """Get the current version of the package"""
    return __version__


def create_pipeline_from_config(config_path: str) -> PipelineManager:
    """
    Convenience function to create a pipeline from a configuration file
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        PipelineManager instance ready for execution
    """
    config_manager = ConfigManager()
    config = config_manager.load_config(config_path)
    return PipelineManager(config)


def run_pipeline_from_config(config_path: str, **overrides) -> bool:
    """
    Convenience function to run a pipeline from a configuration file
    
    Args:
        config_path: Path to the configuration file
        **overrides: Parameter overrides
        
    Returns:
        True if pipeline executed successfully, False otherwise
    """
    try:
        config_manager = ConfigManager()
        config = config_manager.load_config(config_path)
        
        # Apply overrides
        if overrides:
            config_manager.override_parameters(overrides)
        
        # Validate configuration
        if not config_manager.validate_config(config):
            return False
        
        # Run pipeline
        pipeline = PipelineManager(config)
        return pipeline.execute_pipeline()
        
    except Exception as e:
        print(f"Pipeline execution failed: {e}")
        return False


# Package metadata
PACKAGE_INFO = {
    'name': 'dataflow-pipeline',
    'version': __version__,
    'description': 'Flexible data processing framework using DataFlow',
    'author': __author__,
    'author_email': __email__,
    'url': 'https://github.com/your-org/dataflow-pipeline',
    'license': 'Apache 2.0',
    'python_requires': '>=3.8',
    'keywords': ['data-processing', 'pipeline', 'dataflow', 'nlp', 'llm'],
    'classifiers': [
        'Development Status :: 4 - Beta',
        'Intended Audience :: Developers',
        'Intended Audience :: Science/Research',
        'License :: OSI Approved :: Apache Software License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Topic :: Scientific/Engineering :: Artificial Intelligence',
        'Topic :: Software Development :: Libraries :: Python Modules',
    ]
}
