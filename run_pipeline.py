#!/usr/bin/env python3
"""
DataFlow Pipeline Runner

This script provides a simple way to run the DataFlow pipeline
with various configurations and demonstrates the framework usage.
"""

import sys
import argparse
import logging
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from dataflow_pipeline import (
    <PERSON><PERSON>gManager, 
    PipelineManager, 
    create_pipeline_from_config,
    run_pipeline_from_config
)


def setup_logging(log_level="INFO"):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('pipeline_runner.log')
        ]
    )


def run_text_cleaning_example():
    """Run text cleaning pipeline example"""
    print("🧹 Running Text Cleaning Pipeline Example...")
    
    config_path = "configs/examples/text_cleaning_pipeline.yaml"
    
    if not Path(config_path).exists():
        print(f"❌ Configuration file not found: {config_path}")
        print("Please run: python -m dataflow_pipeline create-config --output configs/text_cleaning_pipeline.yaml --template text_cleaning")
        return False
    
    success = run_pipeline_from_config(config_path)
    
    if success:
        print("✅ Text cleaning pipeline completed successfully!")
    else:
        print("❌ Text cleaning pipeline failed!")
    
    return success


def run_qa_generation_example():
    """Run QA generation pipeline example"""
    print("🤖 Running QA Generation Pipeline Example...")
    
    config_path = "configs/examples/qa_generation_pipeline.yaml"
    
    if not Path(config_path).exists():
        print(f"❌ Configuration file not found: {config_path}")
        print("Please run: python -m dataflow_pipeline create-config --output configs/qa_generation_pipeline.yaml --template qa_generation")
        return False
    
    success = run_pipeline_from_config(config_path)
    
    if success:
        print("✅ QA generation pipeline completed successfully!")
    else:
        print("❌ QA generation pipeline failed!")
    
    return success


def run_full_pipeline_example():
    """Run full processing pipeline example"""
    print("🔄 Running Full Processing Pipeline Example...")
    
    config_path = "configs/examples/full_pipeline.yaml"
    
    if not Path(config_path).exists():
        print(f"❌ Configuration file not found: {config_path}")
        print("Please create the full pipeline configuration file first.")
        return False
    
    success = run_pipeline_from_config(config_path)
    
    if success:
        print("✅ Full processing pipeline completed successfully!")
    else:
        print("❌ Full processing pipeline failed!")
    
    return success


def run_custom_pipeline(config_path, **overrides):
    """Run custom pipeline with overrides"""
    print(f"⚙️ Running Custom Pipeline: {config_path}")
    
    if not Path(config_path).exists():
        print(f"❌ Configuration file not found: {config_path}")
        return False
    
    # Apply overrides if provided
    success = run_pipeline_from_config(config_path, **overrides)
    
    if success:
        print("✅ Custom pipeline completed successfully!")
    else:
        print("❌ Custom pipeline failed!")
    
    return success


def demonstrate_api_usage():
    """Demonstrate programmatic API usage"""
    print("📚 Demonstrating Programmatic API Usage...")
    
    try:
        # Method 1: Using convenience function
        print("\n1. Using convenience function:")
        config_path = "configs/examples/text_cleaning_pipeline.yaml"
        if Path(config_path).exists():
            pipeline = create_pipeline_from_config(config_path)
            print(f"   Created pipeline: {pipeline.config.name}")
            print(f"   Operators: {len(pipeline.operators)}")
        
        # Method 2: Manual configuration
        print("\n2. Manual configuration:")
        config_manager = ConfigManager()
        if Path(config_path).exists():
            config = config_manager.load_config(config_path)
            print(f"   Loaded config: {config.name}")
            print(f"   Description: {config.description}")
            
            # Create pipeline manager
            pipeline_manager = PipelineManager(config)
            print(f"   Pipeline info: {pipeline_manager.get_pipeline_info()['name']}")
        
        print("✅ API demonstration completed!")
        return True
        
    except Exception as e:
        print(f"❌ API demonstration failed: {e}")
        return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="DataFlow Pipeline Runner - Examples and Demonstrations"
    )
    
    parser.add_argument(
        '--example', 
        choices=['text_cleaning', 'qa_generation', 'full_pipeline', 'api_demo'],
        help='Run a specific example'
    )
    
    parser.add_argument(
        '--config', 
        help='Custom configuration file path'
    )
    
    parser.add_argument(
        '--input-path', 
        help='Override input path'
    )
    
    parser.add_argument(
        '--output-path', 
        help='Override output path'
    )
    
    parser.add_argument(
        '--parallel-workers', 
        type=int, 
        help='Number of parallel workers'
    )
    
    parser.add_argument(
        '--log-level', 
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Log level'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    print("🚀 DataFlow Pipeline Runner")
    print("=" * 50)
    
    success = False
    
    if args.example:
        if args.example == 'text_cleaning':
            success = run_text_cleaning_example()
        elif args.example == 'qa_generation':
            success = run_qa_generation_example()
        elif args.example == 'full_pipeline':
            success = run_full_pipeline_example()
        elif args.example == 'api_demo':
            success = demonstrate_api_usage()
    
    elif args.config:
        # Prepare overrides
        overrides = {}
        if args.input_path:
            overrides['input_path'] = args.input_path
        if args.output_path:
            overrides['output_path'] = args.output_path
        if args.parallel_workers:
            overrides['parallel_workers'] = args.parallel_workers
        
        success = run_custom_pipeline(args.config, **overrides)
    
    else:
        # Show available examples
        print("Available examples:")
        print("  --example text_cleaning    : Run text cleaning pipeline")
        print("  --example qa_generation    : Run QA generation pipeline")
        print("  --example full_pipeline    : Run full processing pipeline")
        print("  --example api_demo         : Demonstrate API usage")
        print("\nCustom pipeline:")
        print("  --config <path>            : Run custom configuration")
        print("\nFor parallel processing demo:")
        print("  python demo_parallel_processing.py")
        print("\nFor more options, use the CLI:")
        print("  python -m dataflow_pipeline --help")
        print("\nParallel processing examples:")
        print("  python -m dataflow_pipeline run --config configs/parallel_processing_demo.yaml")
        print("  python -m dataflow_pipeline run --config config.yaml --parallel-workers 8 --max-chunk-size 5000")
        
        return 1
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Pipeline runner completed successfully!")
        return 0
    else:
        print("💥 Pipeline runner failed!")
        return 1


if __name__ == '__main__':
    sys.exit(main())
